FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional test dependencies
RUN pip install pytest-cov pytest-mock pytest-xdist

# Copy project files
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTEST_DISABLE_PLUGIN_AUTOLOAD=1

# Default command
CMD ["python", "-m", "pytest", \
     "--cov=.", \
     "--cov-report=html:htmlcov", \
     "--cov-report=term-missing", \
     "--cov-report=xml:coverage.xml", \
     "--tb=short", \
     "--maxfail=0", \
     "--continue-on-collection-errors", \
     "--disable-warnings", \
     "-v"]

# TCP String Search Server

A high-performance, multithreaded TCP server that searches for exact string matches in files. Built with Python 3.10+ and designed for production deployment with SSL support, comprehensive testing, and professional performance analysis.

## Features

- **High Performance**: Optimized hash-set search algorithm with ≤0.5ms response time (cached mode)
- **Multithreaded**: Handles unlimited concurrent connections using ThreadingTCPServer
- **SSL/TLS Support**: Configurable SSL authentication with self-signed certificates
- **Flexible Configuration**: INI-based configuration with hot-reload support
- **REREAD_ON_QUERY Mode**: Choose between cached (fast) or fresh-read (memory-efficient) modes
- **Security Hardened**: Buffer overflow protection, input validation, and secure error handling
- **Professional Testing**: 100% test coverage with comprehensive performance analysis
- **Linux Daemon**: Systemd service integration for production deployment

## Quick Start

### Prerequisites

- Python 3.10 or higher
- Linux operating system (tested on Ubuntu 20.04+)
- Root or sudo access for daemon installation

### Installation

1. **<PERSON>lone and setup the project:**
```bash
git clone <repository-url>
cd tcp-string-search-server
python3 -m pip install -r requirements.txt
```

2. **Generate SSL certificates (optional but recommended):**
```bash
python3 generate_certs.py
```

3. **Configure the server:**
Edit `config.ini` to set your file path and preferences:
```ini
[DEFAULT]
linuxpath = /path/to/your/search/file.txt
reread_on_query = False

[server]
host = 0.0.0.0
port = 8888
max_connections = 200

[ssl]
ssl_enabled = True
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
```

4. **Test the installation:**
```bash
# Start server in foreground
python3 server.py

# In another terminal, test connectivity
python3 client.py --test
```

## Running as a Linux Daemon/Service

### Option 1: User-Space Installation (Recommended for Containers)

For environments where sudo is restricted (like containers), use the user-space installer:

1. **Install in user space:**
```bash
./install_user.sh
```

2. **Start and enable the user service:**
```bash
systemctl --user start tcp-string-search
systemctl --user enable tcp-string-search
systemctl --user status tcp-string-search
```

3. **View logs:**
```bash
journalctl --user -u tcp-string-search -f
```

4. **Use the installed commands:**
```bash
# After installation, these commands are available:
tcp-string-search-server    # Start the server
tcp-string-search-client    # Run the client
tcp-string-search-perf      # Run performance tests
tcp-string-search-gencerts  # Generate SSL certificates
```

### Option 2: System-Wide Installation (Requires sudo)

For traditional system-wide installation with root privileges:

1. **Install the service:**
```bash
sudo ./install_service.sh
```

2. **Start and enable the service:**
```bash
sudo systemctl start tcp-string-search
sudo systemctl enable tcp-string-search
sudo systemctl status tcp-string-search
```

3. **View logs:**
```bash
sudo journalctl -u tcp-string-search -f
```

### Manual Daemon Commands

**User-space service commands:**
```bash
# Start daemon
systemctl --user start tcp-string-search

# Stop daemon
systemctl --user stop tcp-string-search

# Restart daemon
systemctl --user restart tcp-string-search

# Check status
systemctl --user status tcp-string-search

# View logs
journalctl --user -u tcp-string-search --since "1 hour ago"
```

**System-wide service commands (requires sudo):**
```bash
# Start daemon
sudo systemctl start tcp-string-search

# Stop daemon
sudo systemctl stop tcp-string-search

# Restart daemon
sudo systemctl restart tcp-string-search

# Check status
sudo systemctl status tcp-string-search

# View logs
sudo journalctl -u tcp-string-search --since "1 hour ago"
```

## Configuration

### Configuration File Structure

The server uses `config.ini` for all configuration. Key sections:

#### [DEFAULT] - Core Settings
- `linuxpath`: Path to the file to search (required)
- `reread_on_query`: True for fresh reads, False for cached mode (required)

#### [server] - Network Settings  
- `host`: Bind address (default: localhost)
- `port`: Listen port (default: 8888)
- `max_connections`: Maximum concurrent connections (default: 200)
- `max_payload_size`: Maximum query size in bytes (default: 1024)

#### [ssl] - Security Settings
- `ssl_enabled`: Enable/disable SSL (default: False)
- `ssl_cert_file`: Path to SSL certificate
- `ssl_key_file`: Path to SSL private key
- `min_tls_version`: Minimum TLS version (TLSv1.2 or TLSv1.3)

### Performance Modes

**Cached Mode (reread_on_query = False):**
- File loaded once into memory
- Ultra-fast searches (≤0.5ms)
- Higher memory usage
- Best for: Frequent searches, static files

**Reread Mode (reread_on_query = True):**
- File read fresh on each query
- Moderate search speed (≤40ms)
- Minimal memory usage
- Best for: Infrequent searches, changing files

## Usage Examples

### Basic Client Usage

```bash
# Single query
python3 client.py --query "search string"

# Interactive mode
python3 client.py --interactive

# SSL connection
python3 client.py --ssl --query "search string"

# Custom server
python3 client.py --host server.example.com --port 9999 --query "test"
```

### Performance Testing

```bash
# Basic connectivity test
python3 performance_test_client.py --test

# Benchmark performance
python3 performance_test_client.py --benchmark --iterations 100

# Load testing with 50 concurrent clients
python3 performance_test_client.py --load-test --clients 50 --duration 60

# Generate comprehensive performance report
python3 generate_performance_report.py --host localhost --port 8888
```

## Testing

### Run All Tests

```bash
# Complete test suite with coverage
python3 -m pytest --cov=server --cov=client --cov-report=html tests/

# Quick comprehensive test
python3 run_comprehensive_tests.py

# Specific test categories
python3 -m pytest tests/test_server.py -v
python3 -m pytest tests/test_performance_comprehensive.py -v
python3 -m pytest tests/test_exception_handling.py -v
```

### Performance Validation

```bash
# Verify performance requirements
python3 -m pytest tests/test_performance_comprehensive.py::TestPerformanceScaling -v

# Load testing
python3 -m pytest tests/test_load_testing.py -v

# Exception handling coverage
python3 -m pytest tests/test_exception_handling.py -v
```

## Performance Specifications

### Response Time Requirements
- **Cached Mode**: ≤0.5ms per query (REREAD_ON_QUERY=False)
- **Reread Mode**: ≤40ms per query (REREAD_ON_QUERY=True)
- **File Loading**: Optimized for files up to 250,000 lines
- **Concurrent Performance**: Supports 100+ simultaneous connections

### Tested File Sizes
- ✅ 10,000 lines: Sub-millisecond response
- ✅ 50,000 lines: Meets performance targets  
- ✅ 100,000 lines: Optimal performance maintained
- ✅ 250,000 lines: Within specification limits
- ✅ 1,000,000+ lines: Graceful degradation

### Search Algorithms
The server implements and benchmarks 6 different search algorithms:
1. **Hash Set (FrozenSet)** - O(1) lookup, fastest for cached mode
2. **Hash Set (Reread)** - Optimized for REREAD_ON_QUERY mode
3. **Binary Search** - O(log n) with deduplication
4. **Linear Search** - O(n) memory-efficient baseline
5. **Memory-Mapped** - OS-level optimization for large files
6. **Native Grep** - System command wrapper for comparison

## Security Features

### Input Validation
- Maximum payload size enforcement (1024 bytes)
- Buffer overflow protection
- UTF-8 encoding validation
- Null character stripping
- Query sanitization

### SSL/TLS Security
- TLS 1.2+ support with configurable minimum version
- Strong cipher suites (ECDHE-ECDSA-AES256-GCM-SHA384)
- Self-signed certificate generation
- Optional client certificate verification
- Secure error handling without information leakage

### Network Security
- Configurable connection timeouts
- Rate limiting through max_connections
- Graceful connection handling
- Secure socket options (SO_REUSEADDR, TCP_NODELAY)

## Troubleshooting

### Common Issues

**Server won't start:**
```bash
# Check configuration
python3 -c "from server import ServerConfig; ServerConfig()"

# Check file permissions
ls -la config.ini
ls -la /path/to/search/file.txt

# Check port availability
netstat -tlnp | grep :8888
```

**SSL connection errors:**
```bash
# Regenerate certificates
python3 generate_certs.py

# Test SSL configuration
openssl s_client -connect localhost:8888 -servername localhost
```

**Performance issues:**
```bash
# Run performance diagnostics
python3 performance_test_client.py --benchmark --verbose

# Check system resources
htop
iostat -x 1
```

### Log Analysis

```bash
# Server logs
tail -f logs/server.log

# System logs (when running as daemon)
sudo journalctl -u tcp-string-search -f

# Debug mode
python3 server.py --debug
```

## Development

### Code Quality Standards
- **PEP8 Compliance**: Enforced with strict linting
- **PEP20 (Zen of Python)**: Design philosophy adherence  
- **Type Annotations**: Full mypy strict mode compliance
- **Documentation**: Comprehensive docstrings for all functions
- **Testing**: 100% code coverage requirement

### Development Setup

```bash
# Install development dependencies
python3 -m pip install -r requirements.txt

# Run type checking
mypy --strict server.py client.py

# Run linting
flake8 server.py client.py

# Run tests with coverage
python3 -m pytest --cov=server --cov=client --cov-report=term-missing tests/
```

## File Structure

```
tcp-string-search-server/
├── server.py                          # Main server implementation
├── client.py                          # Test client
├── config.ini                         # Configuration file
├── requirements.txt                   # Python dependencies
├── README.md                          # This file
├── search_algorithms_new.py           # Search algorithm implementations
├── generate_certs.py                  # SSL certificate generator
├── install_service.sh                 # Systemd service installer
├── tcp-string-search.service          # Systemd service file
├── performance_test_client.py         # Performance testing client
├── generate_performance_report.py     # Report generator
├── generate_speed_report.py           # Algorithm comparison tool
├── run_comprehensive_tests.py         # Test runner
├── certs/                             # SSL certificates
│   ├── server.crt
│   ├── server.key
│   └── ca.crt
├── logs/                              # Log files
│   └── server.log
├── test_data/                         # Test files
│   └── 200k.txt
├── tests/                             # Test suite
│   ├── test_server.py
│   ├── test_client.py
│   ├── test_performance_comprehensive.py
│   ├── test_load_testing.py
│   └── test_exception_handling.py
├── docs/                              # Documentation and reports
│   ├── speed_report.md
│   └── performance_report.pdf
└── performance_reports/               # Generated performance reports
    ├── charts/
    ├── data/
    └── reports/
```

## License

This project is developed as part of a software engineering assessment. All code is original and follows professional development standards.

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review the comprehensive test suite for usage examples
3. Examine the performance reports for optimization guidance
4. Consult the detailed documentation in `TESTING_DOCUMENTATION.md`

---

**Author**: Brian Kimathi
**Date**: 2025
**Version**: 1.0.0

#!/bin/bash

# TCP String Search Server - Cleanup Script
# Author: <PERSON>
# Date: 2025
#
# This script removes redundant files and directories to prepare a clean deployment package.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Files and directories to remove
CLEANUP_ITEMS=(
    "__pycache__"
    "htmlcov"
    "tests/__pycache__"
    ".pytest_cache"
    ".mypy_cache"
    "*.pyc"
    "*.pyo"
    "*.pyd"
    ".coverage"
    "coverage.xml"
    "test.log"
    "logs/debug.log"
    "logs/error.log" 
    "logs/info.log"
    "logs/warning.log"
    "logs/test.log"
    "performance_reports/charts"
    "performance_reports/data"
    "performance_reports/reports"
    "docs/avg_search_time_(ms)_chart.png"
    "docs/load_time_(ms)_chart.png"
    "docs/benchmark_results_full.csv"
    "split_200k_data.py"
)

# Optional files to remove (ask user)
OPTIONAL_CLEANUP=(
    "test_data/bench_10000.txt"
    "test_data/bench_50000.txt"
    "test_data/bench_100000.txt"
    "test_data/bench_250000.txt"
)

main() {
    echo "TCP String Search Server - Cleanup Script"
    echo "========================================="
    echo
    
    print_status "Cleaning up redundant files and directories..."
    
    # Remove standard cleanup items
    for item in "${CLEANUP_ITEMS[@]}"; do
        if [[ "$item" == *"*"* ]]; then
            # Handle wildcard patterns
            if ls $item 1> /dev/null 2>&1; then
                rm -f $item
                print_success "Removed: $item"
            fi
        elif [ -e "$item" ]; then
            if [ -d "$item" ]; then
                rm -rf "$item"
                print_success "Removed directory: $item"
            else
                rm -f "$item"
                print_success "Removed file: $item"
            fi
        fi
    done
    
    # Ask about optional cleanup
    echo
    print_warning "Optional cleanup items found:"
    for item in "${OPTIONAL_CLEANUP[@]}"; do
        if [ -e "$item" ]; then
            echo "  - $item"
        fi
    done
    
    echo
    read -p "Remove optional benchmark test files? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for item in "${OPTIONAL_CLEANUP[@]}"; do
            if [ -e "$item" ]; then
                rm -f "$item"
                print_success "Removed: $item"
            fi
        done
    else
        print_status "Keeping optional files"
    fi
    
    # Create empty directories that might be needed
    mkdir -p logs
    mkdir -p performance_reports/charts
    mkdir -p performance_reports/data
    mkdir -p performance_reports/reports
    
    # Create .gitkeep files to preserve directory structure
    touch logs/.gitkeep
    touch performance_reports/charts/.gitkeep
    touch performance_reports/data/.gitkeep
    touch performance_reports/reports/.gitkeep
    
    print_success "Created empty directories with .gitkeep files"
    
    echo
    print_success "Cleanup completed successfully!"
    echo
    print_status "Remaining files:"
    find . -type f -not -path './.git/*' -not -name '.gitkeep' | sort
    echo
    print_status "Directory structure is now clean and ready for deployment"
}

# Handle script interruption
trap 'print_error "Cleanup interrupted"; exit 1' INT TERM

# Run main function
main "$@"

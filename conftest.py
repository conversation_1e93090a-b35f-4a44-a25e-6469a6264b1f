"""
Pytest configuration with KeyboardInterrupt protection
"""

import signal
import sys
import pytest
from datetime import datetime

class KeyboardInterruptProtection:
    """Protect pytest from KeyboardInterrupt during test execution"""
    
    def __init__(self):
        self.original_handler = None
        self.interrupt_count = 0
        self.start_time = None
        
    def signal_handler(self, signum, frame):
        """Handle SIGINT gracefully"""
        self.interrupt_count += 1
        current_time = datetime.now()
        
        if self.interrupt_count == 1:
            print(f"\n🛡️  KeyboardInterrupt #{self.interrupt_count} received at {current_time}")
            print("⚠️  Test execution is protected - continuing...")
            print("📊 Monitor progress: tail -f test_results_*.log")
            print("🔄 Press Ctrl+C again within 5 seconds to force exit")
            
        elif self.interrupt_count == 2:
            print(f"\n⚠️  Second KeyboardInterrupt received at {current_time}")
            print("🛑 Forcing exit as requested...")
            # Restore original handler and re-raise
            if self.original_handler:
                signal.signal(signal.SIGINT, self.original_handler)
            raise KeyboardInterrupt("Forced exit after second interrupt")
        else:
            print(f"\n🛑 Multiple interrupts received - forcing exit")
            sys.exit(1)
            
    def enable_protection(self):
        """Enable KeyboardInterrupt protection"""
        self.original_handler = signal.signal(signal.SIGINT, self.signal_handler)
        self.start_time = datetime.now()
        print(f"🛡️  KeyboardInterrupt protection enabled at {self.start_time}")
        
    def disable_protection(self):
        """Disable KeyboardInterrupt protection"""
        if self.original_handler:
            signal.signal(signal.SIGINT, self.original_handler)
            end_time = datetime.now()
            duration = end_time - self.start_time if self.start_time else "unknown"
            print(f"🛡️  KeyboardInterrupt protection disabled at {end_time}")
            if self.interrupt_count > 0:
                print(f"⚠️  Total interrupts blocked: {self.interrupt_count}")

# Global protection instance
_protection = KeyboardInterruptProtection()

def pytest_configure(config):
    """Called after command line options have been parsed"""
    # Enable protection when pytest starts
    _protection.enable_protection()
    
def pytest_unconfigure(config):
    """Called before test process is exited"""
    # Disable protection when pytest ends
    _protection.disable_protection()

def pytest_keyboard_interrupt(excinfo):
    """Called for keyboard interrupt"""
    print(f"\n🛡️  Pytest keyboard interrupt handler called")
    print(f"📊 Exception info: {excinfo}")
    # Don't actually interrupt - let protection handle it
    return True

@pytest.fixture(scope="session", autouse=True)
def keyboard_interrupt_protection():
    """Session-wide fixture to ensure protection is active"""
    print("🛡️  Session-wide KeyboardInterrupt protection active")
    yield
    print("🛡️  Session-wide KeyboardInterrupt protection ending")

# Pytest collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify collected test items"""
    print(f"📊 Collected {len(items)} test items with interrupt protection")

def pytest_runtest_setup(item):
    """Called before each test"""
    # Ensure protection is still active
    if not _protection.original_handler:
        _protection.enable_protection()

# Additional configuration
def pytest_addoption(parser):
    """Add custom command line options"""
    parser.addoption(
        "--interrupt-protection",
        action="store_true",
        default=True,
        help="Enable KeyboardInterrupt protection (default: True)"
    )

def pytest_report_header(config):
    """Add information to test report header"""
    return [
        "🛡️  KeyboardInterrupt Protection: ENABLED",
        "⚠️  Press Ctrl+C twice within 5 seconds to force exit",
        "📊 Monitor progress: tail -f test_results_*.log"
    ]

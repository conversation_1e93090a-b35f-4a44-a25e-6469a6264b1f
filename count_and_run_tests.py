#!/usr/bin/env python3
"""
Count and run tests to verify comprehensive coverage.
"""

import os
import sys
import subprocess
import time

def count_tests():
    """Count the total number of tests."""
    print("Counting tests...")
    
    # Change to the correct directory
    os.chdir('/home/<USER>/CODES/interview')
    
    try:
        # Run pytest to collect tests
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 'tests/', '--collect-only', '-q'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            test_lines = [line for line in lines if '::' in line and 'test_' in line]
            
            print(f"Total number of tests: {len(test_lines)}")
            
            # Count by file
            file_counts = {}
            for line in test_lines:
                file_name = line.split('::')[0]
                file_counts[file_name] = file_counts.get(file_name, 0) + 1
            
            print("\nTest breakdown by file:")
            for file_name, count in sorted(file_counts.items()):
                print(f"  {file_name}: {count} tests")
            
            return len(test_lines)
        else:
            print(f"Error collecting tests: {result.stderr}")
            return 0
            
    except subprocess.TimeoutExpired:
        print("Test collection timed out")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 0

def run_tests():
    """Run a subset of tests to verify they work."""
    print("\nRunning a sample of tests...")
    
    test_files = [
        'tests/test_config.py',
        'tests/test_client.py::TestSearchClient::test_client_initialization',
        'tests/test_server.py::TestFileSearchEngine::test_search_engine_cached_mode',
        'tests/test_search_algorithms.py::TestHashSetSearch::test_initialization',
        'tests/test_exception_handling.py::TestFileSearchEngineExceptions::test_file_not_found_exception'
    ]
    
    for test in test_files:
        print(f"\nRunning: {test}")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', test, '-v', '--tb=short'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✓ PASSED")
            else:
                print("✗ FAILED")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("✗ TIMEOUT")
        except Exception as e:
            print(f"✗ ERROR: {e}")

def main():
    print("TCP String Search Server - Test Analysis")
    print("=" * 50)
    
    # Count tests
    total_tests = count_tests()
    
    if total_tests >= 200:
        print(f"\n✓ SUCCESS: Found {total_tests} tests (requirement: 200+)")
    else:
        print(f"\n✗ INSUFFICIENT: Found {total_tests} tests (requirement: 200+)")
    
    # Run sample tests
    run_tests()
    
    print(f"\nAnalysis complete. Total tests: {total_tests}")
    return 0

if __name__ == "__main__":
    sys.exit(main())

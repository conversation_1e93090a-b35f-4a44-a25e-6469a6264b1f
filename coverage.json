{"meta": {"format": 3, "version": "7.8.2", "timestamp": "2025-06-07T02:18:21.965851", "branch_coverage": false, "show_contexts": false}, "files": {"__init__.py": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "client.py": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 24, 25, 32, 51, 52, 53, 54, 55, 56, 58, 59, 60, 61, 63, 76, 79, 80, 81, 82, 85, 86, 92, 95, 97, 121, 130, 164, 181, 195, 196, 198, 200, 201, 204, 205, 206, 209, 212, 213, 214, 216, 219, 222, 224, 226, 227, 228, 229, 231, 232, 234, 279, 286, 287, 288, 289, 290, 293, 300, 301, 302, 324, 329, 331, 332, 334, 337, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 363, 366, 379, 380, 381, 382, 383, 385, 386, 389, 390, 391, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 414, 417], "summary": {"covered_lines": 111, "num_statements": 201, "percent_covered": 55.223880597014926, "percent_covered_display": "55.22", "missing_lines": 90, "excluded_lines": 2}, "missing_lines": [65, 70, 71, 72, 73, 74, 87, 89, 93, 99, 100, 102, 104, 105, 108, 109, 110, 113, 114, 116, 117, 118, 119, 123, 124, 125, 126, 127, 128, 132, 133, 135, 136, 138, 140, 141, 142, 144, 147, 148, 150, 152, 153, 154, 155, 156, 157, 158, 160, 161, 162, 179, 236, 237, 238, 239, 241, 242, 243, 244, 245, 247, 248, 250, 252, 253, 256, 257, 258, 259, 260, 262, 263, 264, 265, 266, 268, 269, 270, 271, 272, 273, 274, 277, 311, 313, 393, 394, 395, 396], "excluded_lines": [417, 418], "functions": {"SearchClient.__init__": {"executed_lines": [51, 52, 53, 54, 55, 56, 58, 59, 60, 61], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchClient._find_ca_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [65, 70, 71, 72, 73, 74], "excluded_lines": []}, "SearchClient.create_ssl_context": {"executed_lines": [79, 80, 81, 82, 85, 86, 92, 95], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.72727272727273, "percent_covered_display": "72.73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [87, 89, 93], "excluded_lines": []}, "SearchClient.connect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [99, 100, 102, 104, 105, 108, 109, 110, 113, 114, 116, 117, 118, 119], "excluded_lines": []}, "SearchClient.disconnect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [123, 124, 125, 126, 127, 128], "excluded_lines": []}, "SearchClient.send_query_persistent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [132, 133, 135, 136, 138, 140, 141, 142, 144, 147, 148, 150, 152, 153, 154, 155, 156, 157, 158, 160, 161, 162], "excluded_lines": []}, "SearchClient.send_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [179], "excluded_lines": []}, "SearchClient.search": {"executed_lines": [195, 196, 198, 200, 201, 204, 205, 206, 209, 212, 213, 214, 216, 219, 222, 224, 226, 227, 228, 229, 231, 232], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchClient.interactive_mode": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [236, 237, 238, 239, 241, 242, 243, 244, 245, 247, 248, 250, 252, 253, 256, 257, 258, 259, 260, 262, 263, 264, 265, 266, 268, 269, 270, 271, 272, 273, 274, 277], "excluded_lines": []}, "SearchClient.test_connection": {"executed_lines": [286, 287, 288, 289, 290], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "load_config": {"executed_lines": [300, 301, 302], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [311, 313], "excluded_lines": []}, "main": {"executed_lines": [329, 331, 332, 334, 337, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 363, 366, 379, 380, 381, 382, 383, 385, 386, 389, 390, 391, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 414], "summary": {"covered_lines": 41, "num_statements": 45, "percent_covered": 91.11111111111111, "percent_covered_display": "91.11", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [393, 394, 395, 396], "excluded_lines": []}, "": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 24, 25, 32, 63, 76, 97, 121, 130, 164, 181, 234, 279, 293, 324, 417], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [417, 418]}}, "classes": {"SearchClient": {"executed_lines": [51, 52, 53, 54, 55, 56, 58, 59, 60, 61, 79, 80, 81, 82, 85, 86, 92, 95, 195, 196, 198, 200, 201, 204, 205, 206, 209, 212, 213, 214, 216, 219, 222, 224, 226, 227, 228, 229, 231, 232, 286, 287, 288, 289, 290], "summary": {"covered_lines": 45, "num_statements": 129, "percent_covered": 34.883720930232556, "percent_covered_display": "34.88", "missing_lines": 84, "excluded_lines": 0}, "missing_lines": [65, 70, 71, 72, 73, 74, 87, 89, 93, 99, 100, 102, 104, 105, 108, 109, 110, 113, 114, 116, 117, 118, 119, 123, 124, 125, 126, 127, 128, 132, 133, 135, 136, 138, 140, 141, 142, 144, 147, 148, 150, 152, 153, 154, 155, 156, 157, 158, 160, 161, 162, 179, 236, 237, 238, 239, 241, 242, 243, 244, 245, 247, 248, 250, 252, 253, 256, 257, 258, 259, 260, 262, 263, 264, 265, 266, 268, 269, 270, 271, 272, 273, 274, 277], "excluded_lines": []}, "": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 24, 25, 32, 63, 76, 97, 121, 130, 164, 181, 234, 279, 293, 300, 301, 302, 324, 329, 331, 332, 334, 337, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 363, 366, 379, 380, 381, 382, 383, 385, 386, 389, 390, 391, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 414, 417], "summary": {"covered_lines": 66, "num_statements": 72, "percent_covered": 91.66666666666667, "percent_covered_display": "91.67", "missing_lines": 6, "excluded_lines": 2}, "missing_lines": [311, 313, 393, 394, 395, 396], "excluded_lines": [417, 418]}}}, "config_loader.py": {"executed_lines": [2, 9, 10, 11, 12, 13, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 55, 70, 71, 81, 91, 96, 97, 98, 99, 106, 119, 120, 122, 125, 127, 128, 133, 134, 135, 136, 138, 156, 157, 160, 163, 165, 168, 169, 172, 173, 175, 178, 196, 198, 199, 201, 202, 205, 207, 218, 219, 229, 237, 238, 245, 246, 247, 249, 252, 255], "summary": {"covered_lines": 79, "num_statements": 124, "percent_covered": 63.70967741935484, "percent_covered_display": "63.71", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [73, 74, 75, 76, 78, 101, 123, 129, 130, 142, 143, 144, 145, 147, 148, 149, 150, 151, 152, 153, 158, 164, 166, 170, 174, 176, 187, 188, 189, 190, 191, 192, 200, 203, 209, 231, 250, 265, 267, 268, 270, 271, 273, 274, 276], "excluded_lines": [], "functions": {"_validate_boolean": {"executed_lines": [70, 71], "summary": {"covered_lines": 2, "num_statements": 7, "percent_covered": 28.571428571428573, "percent_covered_display": "28.57", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [73, 74, 75, 76, 78], "excluded_lines": []}, "find_config_file": {"executed_lines": [91, 96, 97, 98, 99], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.33333333333333, "percent_covered_display": "83.33", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [101], "excluded_lines": []}, "load_config": {"executed_lines": [119, 120, 122, 125, 127, 128, 133, 134, 135, 136, 138, 156, 157, 160, 163, 165, 168, 169, 172, 173, 175, 178, 196, 198, 207, 218, 219, 229, 237, 238, 245, 246, 247, 249, 252], "summary": {"covered_lines": 35, "num_statements": 64, "percent_covered": 54.6875, "percent_covered_display": "54.69", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [123, 129, 130, 142, 143, 144, 145, 147, 148, 149, 150, 151, 152, 153, 158, 164, 166, 170, 174, 176, 187, 188, 189, 190, 191, 192, 209, 231, 250], "excluded_lines": []}, "load_config.resolve_cert_path": {"executed_lines": [199, 201, 202, 205], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "66.67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [200, 203], "excluded_lines": []}, "get_file_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [265, 267, 268, 270, 271, 273, 274, 276], "excluded_lines": []}, "": {"executed_lines": [2, 9, 10, 11, 12, 13, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 55, 81, 106, 255], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DefaultConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ServerConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SSLConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LoggingConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [2, 9, 10, 11, 12, 13, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 55, 70, 71, 81, 91, 96, 97, 98, 99, 106, 119, 120, 122, 125, 127, 128, 133, 134, 135, 136, 138, 156, 157, 160, 163, 165, 168, 169, 172, 173, 175, 178, 196, 198, 199, 201, 202, 205, 207, 218, 219, 229, 237, 238, 245, 246, 247, 249, 252, 255], "summary": {"covered_lines": 79, "num_statements": 124, "percent_covered": 63.70967741935484, "percent_covered_display": "63.71", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [73, 74, 75, 76, 78, 101, 123, 129, 130, 142, 143, 144, 145, 147, 148, 149, 150, 151, 152, 153, 158, 164, 166, 170, 174, 176, 187, 188, 189, 190, 191, 192, 200, 203, 209, 231, 250, 265, 267, 268, 270, 271, 273, 274, 276], "excluded_lines": []}}}, "count_and_run_tests.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 55, "excluded_lines": 2}, "missing_lines": [6, 7, 8, 9, 11, 13, 16, 18, 20, 24, 25, 26, 28, 31, 32, 33, 34, 36, 37, 38, 40, 42, 43, 45, 46, 47, 48, 49, 50, 52, 54, 56, 64, 65, 66, 67, 71, 72, 74, 75, 76, 78, 79, 80, 81, 83, 84, 85, 88, 90, 91, 93, 96, 98, 99], "excluded_lines": [101, 102], "functions": {"count_tests": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [13, 16, 18, 20, 24, 25, 26, 28, 31, 32, 33, 34, 36, 37, 38, 40, 42, 43, 45, 46, 47, 48, 49, 50], "excluded_lines": []}, "run_tests": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [54, 56, 64, 65, 66, 67, 71, 72, 74, 75, 76, 78, 79, 80, 81], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [84, 85, 88, 90, 91, 93, 96, 98, 99], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 2}, "missing_lines": [6, 7, 8, 9, 11, 52, 83], "excluded_lines": [101, 102]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 55, "excluded_lines": 2}, "missing_lines": [6, 7, 8, 9, 11, 13, 16, 18, 20, 24, 25, 26, 28, 31, 32, 33, 34, 36, 37, 38, 40, 42, 43, 45, 46, 47, 48, 49, 50, 52, 54, 56, 64, 65, 66, 67, 71, 72, 74, 75, 76, 78, 79, 80, 81, 83, 84, 85, 88, 90, 91, 93, 96, 98, 99], "excluded_lines": [101, 102]}}}, "create_wrappers.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 41, "excluded_lines": 2}, "missing_lines": [6, 7, 9, 10, 11, 12, 13, 16, 19, 42, 43, 44, 45, 46, 47, 48, 50, 52, 53, 54, 57, 63, 65, 66, 68, 70, 71, 73, 74, 76, 82, 84, 85, 87, 89, 90, 92, 93, 94, 95, 96], "excluded_lines": [98, 99], "functions": {"main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 16, 19, 42, 43, 44, 45, 46, 47, 48, 50, 52, 53, 54, 57, 63, 65, 66, 68, 70, 71, 73, 74, 76, 82, 84, 85, 87, 89, 90, 92, 93, 94, 95, 96], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 2}, "missing_lines": [6, 7, 9], "excluded_lines": [98, 99]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 41, "excluded_lines": 2}, "missing_lines": [6, 7, 9, 10, 11, 12, 13, 16, 19, 42, 43, 44, 45, 46, 47, 48, 50, 52, 53, 54, 57, 63, 65, 66, 68, 70, 71, 73, 74, 76, 82, 84, 85, 87, 89, 90, 92, 93, 94, 95, 96], "excluded_lines": [98, 99]}}}, "generate_certs.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 147, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 147, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 19, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 51, 52, 53, 54, 57, 67, 68, 71, 75, 76, 79, 85, 88, 103, 104, 105, 106, 108, 109, 110, 111, 112, 115, 119, 120, 123, 128, 129, 132, 133, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 155, 158, 159, 161, 164, 174, 175, 176, 177, 178, 181, 185, 186, 189, 194, 195, 198, 204, 207, 209, 212, 219, 221, 224, 225, 226, 228, 230, 231, 232, 235, 245, 246, 248, 249, 250, 253, 257, 260, 264, 267, 268, 269, 271, 273, 274, 276, 278, 279, 280, 283, 284, 285, 286, 287, 288, 289, 290, 291, 294, 297, 298, 299, 300, 301, 302, 304, 307, 308, 309, 311, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 329, 330], "excluded_lines": [333, 334], "functions": {"run_command": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], "excluded_lines": []}, "create_cert_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [51, 52, 53, 54], "excluded_lines": []}, "generate_ca_certificate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [67, 68, 71, 75, 76, 79, 85], "excluded_lines": []}, "generate_server_certificate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [103, 104, 105, 106, 108, 109, 110, 111, 112, 115, 119, 120, 123, 128, 129, 132, 133, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 155, 158, 159, 161], "excluded_lines": []}, "generate_client_certificate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [174, 175, 176, 177, 178, 181, 185, 186, 189, 194, 195, 198, 204, 207, 209], "excluded_lines": []}, "set_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [219, 221, 224, 225, 226, 228, 230, 231, 232], "excluded_lines": []}, "verify_certificates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [245, 246, 248, 249, 250, 253, 257], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 49, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 49, "excluded_lines": 0}, "missing_lines": [264, 267, 268, 269, 271, 273, 274, 276, 278, 279, 280, 283, 284, 285, 286, 287, 288, 289, 290, 291, 294, 297, 298, 299, 300, 301, 302, 304, 307, 308, 309, 311, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 329, 330], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 19, 44, 57, 88, 164, 212, 235, 260], "excluded_lines": [333, 334]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 147, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 147, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 19, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 51, 52, 53, 54, 57, 67, 68, 71, 75, 76, 79, 85, 88, 103, 104, 105, 106, 108, 109, 110, 111, 112, 115, 119, 120, 123, 128, 129, 132, 133, 134, 135, 136, 137, 138, 141, 142, 145, 146, 149, 155, 158, 159, 161, 164, 174, 175, 176, 177, 178, 181, 185, 186, 189, 194, 195, 198, 204, 207, 209, 212, 219, 221, 224, 225, 226, 228, 230, 231, 232, 235, 245, 246, 248, 249, 250, 253, 257, 260, 264, 267, 268, 269, 271, 273, 274, 276, 278, 279, 280, 283, 284, 285, 286, 287, 288, 289, 290, 291, 294, 297, 298, 299, 300, 301, 302, 304, 307, 308, 309, 311, 312, 313, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 329, 330], "excluded_lines": [333, 334]}}}, "generate_performance_report.py": {"executed_lines": [2, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 43, 45, 46, 49, 50, 52, 63, 127, 151, 186, 221, 253, 343, 352, 525, 573, 605], "summary": {"covered_lines": 34, "num_statements": 335, "percent_covered": 10.149253731343284, "percent_covered_display": "10.15", "missing_lines": 301, "excluded_lines": 2}, "missing_lines": [37, 38, 39, 40, 54, 55, 56, 59, 60, 61, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81, 82, 84, 91, 92, 93, 94, 97, 98, 100, 101, 102, 103, 106, 107, 108, 109, 110, 112, 115, 116, 117, 120, 121, 122, 123, 125, 129, 132, 133, 134, 135, 138, 139, 140, 141, 144, 145, 146, 147, 149, 153, 155, 156, 157, 158, 160, 162, 165, 170, 171, 172, 173, 174, 177, 178, 179, 182, 183, 184, 188, 189, 190, 191, 193, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 210, 211, 212, 213, 214, 215, 217, 218, 219, 223, 226, 228, 229, 234, 236, 239, 240, 241, 244, 247, 249, 250, 251, 255, 257, 258, 259, 260, 263, 264, 265, 266, 267, 268, 269, 272, 273, 274, 275, 277, 278, 279, 280, 282, 283, 284, 285, 286, 287, 288, 289, 292, 293, 294, 295, 297, 298, 299, 301, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 316, 317, 318, 319, 322, 324, 325, 327, 328, 329, 330, 332, 334, 335, 336, 337, 339, 341, 345, 347, 348, 350, 354, 355, 356, 358, 361, 362, 363, 366, 375, 384, 385, 386, 389, 390, 391, 392, 395, 396, 397, 398, 408, 409, 419, 421, 424, 425, 426, 427, 430, 437, 438, 444, 445, 448, 459, 460, 466, 468, 471, 472, 474, 475, 476, 477, 484, 485, 495, 498, 499, 501, 502, 503, 505, 506, 507, 510, 511, 512, 513, 514, 517, 518, 519, 520, 521, 522, 523, 527, 528, 531, 534, 537, 538, 541, 542, 545, 546, 547, 548, 549, 550, 551, 553, 559, 560, 562, 563, 565, 566, 567, 568, 570, 575, 577, 578, 579, 580, 581, 582, 584, 587, 588, 589, 590, 592, 594, 595, 597, 598, 599, 600, 602], "excluded_lines": [605, 606], "functions": {"PerformanceReportGenerator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 59, 60, 61], "excluded_lines": []}, "PerformanceReportGenerator.run_server_performance_tests": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 40, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81, 82, 84, 91, 92, 93, 94, 97, 98, 100, 101, 102, 103, 106, 107, 108, 109, 110, 112, 115, 116, 117, 120, 121, 122, 123, 125], "excluded_lines": []}, "PerformanceReportGenerator.generate_performance_charts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [129, 132, 133, 134, 135, 138, 139, 140, 141, 144, 145, 146, 147, 149], "excluded_lines": []}, "PerformanceReportGenerator._create_benchmark_chart": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [153, 155, 156, 157, 158, 160, 162, 165, 170, 171, 172, 173, 174, 177, 178, 179, 182, 183, 184], "excluded_lines": []}, "PerformanceReportGenerator._create_load_test_chart": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [188, 189, 190, 191, 193, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 210, 211, 212, 213, 214, 215, 217, 218, 219], "excluded_lines": []}, "PerformanceReportGenerator._create_sustained_load_chart": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [223, 226, 228, 229, 234, 236, 239, 240, 241, 244, 247, 249, 250, 251], "excluded_lines": []}, "PerformanceReportGenerator.generate_markdown_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 64, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 64, "excluded_lines": 0}, "missing_lines": [255, 257, 258, 259, 260, 263, 264, 265, 266, 267, 268, 269, 272, 273, 274, 275, 277, 278, 279, 280, 282, 283, 284, 285, 286, 287, 288, 289, 292, 293, 294, 295, 297, 298, 299, 301, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 316, 317, 318, 319, 322, 324, 325, 327, 328, 329, 330, 332, 334, 335, 336, 337, 339, 341], "excluded_lines": []}, "PerformanceReportGenerator.save_raw_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [345, 347, 348, 350], "excluded_lines": []}, "PerformanceReportGenerator.generate_pdf_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [354, 355, 356, 358, 361, 362, 363, 366, 375, 384, 385, 386, 389, 390, 391, 392, 395, 396, 397, 398, 408, 409, 419, 421, 424, 425, 426, 427, 430, 437, 438, 444, 445, 448, 459, 460, 466, 468, 471, 472, 474, 475, 476, 477, 484, 485, 495, 498, 499, 501, 502, 503, 505, 506, 507, 510, 511, 512, 513, 514, 517, 518, 519, 520, 521, 522, 523], "excluded_lines": []}, "PerformanceReportGenerator.generate_full_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [527, 528, 531, 534, 537, 538, 541, 542, 545, 546, 547, 548, 549, 550, 551, 553, 559, 560, 562, 563, 565, 566, 567, 568, 570], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [575, 577, 578, 579, 580, 581, 582, 584, 587, 588, 589, 590, 592, 594, 595, 597, 598, 599, 600, 602], "excluded_lines": []}, "": {"executed_lines": [2, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 43, 45, 46, 49, 50, 52, 63, 127, 151, 186, 221, 253, 343, 352, 525, 573, 605], "summary": {"covered_lines": 34, "num_statements": 38, "percent_covered": 89.47368421052632, "percent_covered_display": "89.47", "missing_lines": 4, "excluded_lines": 2}, "missing_lines": [37, 38, 39, 40], "excluded_lines": [605, 606]}}, "classes": {"PerformanceReportGenerator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 277, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 277, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 59, 60, 61, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81, 82, 84, 91, 92, 93, 94, 97, 98, 100, 101, 102, 103, 106, 107, 108, 109, 110, 112, 115, 116, 117, 120, 121, 122, 123, 125, 129, 132, 133, 134, 135, 138, 139, 140, 141, 144, 145, 146, 147, 149, 153, 155, 156, 157, 158, 160, 162, 165, 170, 171, 172, 173, 174, 177, 178, 179, 182, 183, 184, 188, 189, 190, 191, 193, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 210, 211, 212, 213, 214, 215, 217, 218, 219, 223, 226, 228, 229, 234, 236, 239, 240, 241, 244, 247, 249, 250, 251, 255, 257, 258, 259, 260, 263, 264, 265, 266, 267, 268, 269, 272, 273, 274, 275, 277, 278, 279, 280, 282, 283, 284, 285, 286, 287, 288, 289, 292, 293, 294, 295, 297, 298, 299, 301, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 316, 317, 318, 319, 322, 324, 325, 327, 328, 329, 330, 332, 334, 335, 336, 337, 339, 341, 345, 347, 348, 350, 354, 355, 356, 358, 361, 362, 363, 366, 375, 384, 385, 386, 389, 390, 391, 392, 395, 396, 397, 398, 408, 409, 419, 421, 424, 425, 426, 427, 430, 437, 438, 444, 445, 448, 459, 460, 466, 468, 471, 472, 474, 475, 476, 477, 484, 485, 495, 498, 499, 501, 502, 503, 505, 506, 507, 510, 511, 512, 513, 514, 517, 518, 519, 520, 521, 522, 523, 527, 528, 531, 534, 537, 538, 541, 542, 545, 546, 547, 548, 549, 550, 551, 553, 559, 560, 562, 563, 565, 566, 567, 568, 570], "excluded_lines": []}, "": {"executed_lines": [2, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 43, 45, 46, 49, 50, 52, 63, 127, 151, 186, 221, 253, 343, 352, 525, 573, 605], "summary": {"covered_lines": 34, "num_statements": 58, "percent_covered": 58.62068965517241, "percent_covered_display": "58.62", "missing_lines": 24, "excluded_lines": 2}, "missing_lines": [37, 38, 39, 40, 575, 577, 578, 579, 580, 581, 582, 584, 587, 588, 589, 590, 592, 594, 595, 597, 598, 599, 600, 602], "excluded_lines": [605, 606]}}}, "generate_speed_report.py": {"executed_lines": [2, 21, 22, 23, 24, 25, 26, 27, 28, 29, 33, 39, 40, 41, 44, 47, 107, 144, 206, 214, 323], "summary": {"covered_lines": 15, "num_statements": 119, "percent_covered": 12.605042016806722, "percent_covered_display": "12.61", "missing_lines": 104, "excluded_lines": 10}, "missing_lines": [75, 76, 77, 79, 80, 83, 84, 85, 86, 88, 89, 91, 92, 93, 94, 95, 96, 98, 127, 128, 130, 131, 132, 133, 136, 141, 163, 164, 165, 167, 168, 173, 174, 176, 177, 179, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 193, 194, 195, 196, 197, 198, 199, 200, 202, 221, 222, 224, 225, 235, 238, 239, 240, 241, 243, 244, 246, 247, 248, 249, 250, 251, 253, 260, 262, 263, 265, 268, 271, 274, 275, 278, 283, 285, 286, 287, 294, 295, 297, 299, 300, 303, 304, 305, 306, 307, 310, 311, 312, 315, 318, 319, 320], "excluded_lines": [39, 40, 41, 42, 43, 44, 323, 324, 325, 326], "functions": {"SearchAlgorithm.load": {"executed_lines": [40], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [40]}, "SearchAlgorithm.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [42]}, "SearchAlgorithm.name": {"executed_lines": [44], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [44]}, "benchmark_algorithm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [75, 76, 77, 79, 80, 83, 84, 85, 86, 88, 89, 91, 92, 93, 94, 95, 96, 98], "excluded_lines": []}, "create_test_queries": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [127, 128, 130, 131, 132, 133, 136, 141], "excluded_lines": []}, "plot_comparison": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [163, 164, 165, 167, 168, 173, 174, 176, 177, 179, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 193, 194, 195, 196, 197, 198, 199, 200, 202], "excluded_lines": []}, "generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 48, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [221, 222, 224, 225, 235, 238, 239, 240, 241, 243, 244, 246, 247, 248, 249, 250, 251, 253, 260, 262, 263, 265, 268, 271, 274, 275, 278, 283, 285, 286, 287, 294, 295, 297, 299, 300, 303, 304, 305, 306, 307, 310, 311, 312, 315, 318, 319, 320], "excluded_lines": []}, "": {"executed_lines": [2, 21, 22, 23, 24, 25, 26, 27, 28, 29, 33, 39, 41, 47, 107, 144, 206, 214, 323], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [39, 41, 43, 323, 324, 325, 326]}}, "classes": {"SearchAlgorithm": {"executed_lines": [40, 44], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 3}, "missing_lines": [], "excluded_lines": [40, 42, 44]}, "": {"executed_lines": [2, 21, 22, 23, 24, 25, 26, 27, 28, 29, 33, 39, 41, 47, 107, 144, 206, 214, 323], "summary": {"covered_lines": 15, "num_statements": 119, "percent_covered": 12.605042016806722, "percent_covered_display": "12.61", "missing_lines": 104, "excluded_lines": 7}, "missing_lines": [75, 76, 77, 79, 80, 83, 84, 85, 86, 88, 89, 91, 92, 93, 94, 95, 96, 98, 127, 128, 130, 131, 132, 133, 136, 141, 163, 164, 165, 167, 168, 173, 174, 176, 177, 179, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 193, 194, 195, 196, 197, 198, 199, 200, 202, 221, 222, 224, 225, 235, 238, 239, 240, 241, 243, 244, 246, 247, 248, 249, 250, 251, 253, 260, 262, 263, 265, 268, 271, 274, 275, 278, 283, 285, 286, 287, 294, 295, 297, 299, 300, 303, 304, 305, 306, 307, 310, 311, 312, 315, 318, 319, 320], "excluded_lines": [39, 41, 43, 323, 324, 325, 326]}}}, "performance_test_client.py": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 34, 51, 65, 98, 187, 321, 330, 358, 489], "summary": {"covered_lines": 19, "num_statements": 185, "percent_covered": 10.27027027027027, "percent_covered_display": "10.27", "missing_lines": 166, "excluded_lines": 2}, "missing_lines": [45, 46, 47, 48, 49, 53, 54, 56, 57, 58, 59, 60, 62, 63, 72, 74, 75, 78, 79, 80, 82, 85, 87, 89, 91, 92, 94, 95, 96, 109, 111, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 126, 127, 128, 130, 133, 135, 137, 147, 149, 155, 156, 169, 182, 200, 201, 202, 203, 205, 206, 208, 210, 211, 212, 214, 215, 216, 218, 226, 229, 231, 232, 235, 236, 238, 239, 240, 241, 244, 245, 247, 248, 251, 252, 253, 254, 256, 257, 273, 289, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 316, 323, 324, 325, 326, 327, 332, 360, 382, 384, 386, 388, 392, 394, 396, 400, 402, 406, 408, 412, 414, 417, 420, 427, 430, 431, 432, 433, 434, 436, 437, 438, 441, 442, 443, 445, 447, 448, 449, 452, 453, 454, 455, 456, 457, 458, 459, 462, 463, 464, 466, 468, 469, 470, 473, 474, 475, 476, 479, 480, 481, 482, 483, 484, 485, 486], "excluded_lines": [489, 490], "functions": {"PerformanceTestClient.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49], "excluded_lines": []}, "PerformanceTestClient.create_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [53, 54, 56, 57, 58, 59, 60, 62, 63], "excluded_lines": []}, "PerformanceTestClient.single_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [72, 74, 75, 78, 79, 80, 82, 85, 87, 89, 91, 92, 94, 95, 96], "excluded_lines": []}, "PerformanceTestClient.benchmark_queries": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [109, 111, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 126, 127, 128, 130, 133, 135, 137, 147, 149, 155, 156, 169, 182], "excluded_lines": []}, "PerformanceTestClient.concurrent_load_test": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [200, 201, 202, 203, 205, 206, 208, 235, 236, 238, 239, 240, 241, 244, 245, 247, 248, 251, 252, 253, 254, 256, 257, 273, 289, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 316], "excluded_lines": []}, "PerformanceTestClient.concurrent_load_test.worker_thread": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [210, 211, 212, 214, 215, 216, 218, 226, 229, 231, 232], "excluded_lines": []}, "PerformanceTestClient.test_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [323, 324, 325, 326, 327], "excluded_lines": []}, "create_test_queries": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [332], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 59, "excluded_lines": 0}, "missing_lines": [360, 382, 384, 386, 388, 392, 394, 396, 400, 402, 406, 408, 412, 414, 417, 420, 427, 430, 431, 432, 433, 434, 436, 437, 438, 441, 442, 443, 445, 447, 448, 449, 452, 453, 454, 455, 456, 457, 458, 459, 462, 463, 464, 466, 468, 469, 470, 473, 474, 475, 476, 479, 480, 481, 482, 483, 484, 485, 486], "excluded_lines": []}, "": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 34, 51, 65, 98, 187, 321, 330, 358, 489], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [489, 490]}}, "classes": {"PerformanceTestClient": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 106, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 106, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49, 53, 54, 56, 57, 58, 59, 60, 62, 63, 72, 74, 75, 78, 79, 80, 82, 85, 87, 89, 91, 92, 94, 95, 96, 109, 111, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 126, 127, 128, 130, 133, 135, 137, 147, 149, 155, 156, 169, 182, 200, 201, 202, 203, 205, 206, 208, 210, 211, 212, 214, 215, 216, 218, 226, 229, 231, 232, 235, 236, 238, 239, 240, 241, 244, 245, 247, 248, 251, 252, 253, 254, 256, 257, 273, 289, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 316, 323, 324, 325, 326, 327], "excluded_lines": []}, "": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 34, 51, 65, 98, 187, 321, 330, 358, 489], "summary": {"covered_lines": 19, "num_statements": 79, "percent_covered": 24.050632911392405, "percent_covered_display": "24.05", "missing_lines": 60, "excluded_lines": 2}, "missing_lines": [332, 360, 382, 384, 386, 388, 392, 394, 396, 400, 402, 406, 408, 412, 414, 417, 420, 427, 430, 431, 432, 433, 434, 436, 437, 438, 441, 442, 443, 445, 447, 448, 449, 452, 453, 454, 455, 456, 457, 458, 459, 462, 463, 464, 466, 468, 469, 470, 473, 474, 475, 476, 479, 480, 481, 482, 483, 484, 485, 486], "excluded_lines": [489, 490]}}}, "run_comprehensive_tests.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 84, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 84, "excluded_lines": 3}, "missing_lines": [12, 13, 14, 15, 16, 19, 21, 22, 23, 24, 26, 27, 29, 30, 31, 33, 34, 35, 37, 38, 39, 41, 42, 44, 45, 46, 47, 48, 49, 52, 54, 55, 58, 59, 61, 64, 70, 76, 82, 88, 94, 100, 106, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144, 146, 147, 148, 149, 150, 151, 152, 153, 154, 156, 158, 159], "excluded_lines": [162, 163, 164], "functions": {"run_command": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [21, 22, 23, 24, 26, 27, 29, 30, 31, 33, 34, 35, 37, 38, 39, 41, 42, 44, 45, 46, 47, 48, 49], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 54, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [54, 55, 58, 59, 61, 64, 70, 76, 82, 88, 94, 100, 106, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144, 146, 147, 148, 149, 150, 151, 152, 153, 154, 156, 158, 159], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 3}, "missing_lines": [12, 13, 14, 15, 16, 19, 52], "excluded_lines": [162, 163, 164]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 84, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 84, "excluded_lines": 3}, "missing_lines": [12, 13, 14, 15, 16, 19, 21, 22, 23, 24, 26, 27, 29, 30, 31, 33, 34, 35, 37, 38, 39, 41, 42, 44, 45, 46, 47, 48, 49, 52, 54, 55, 58, 59, 61, 64, 70, 76, 82, 88, 94, 100, 106, 112, 113, 114, 116, 117, 119, 120, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 140, 141, 142, 143, 144, 146, 147, 148, 149, 150, 151, 152, 153, 154, 156, 158, 159], "excluded_lines": [162, 163, 164]}}}, "run_uninterruptible_tests.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 66, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 66, "excluded_lines": 2}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 22, 23, 24, 25, 26, 27, 30, 32, 34, 36, 37, 39, 41, 42, 45, 62, 63, 64, 66, 67, 68, 69, 70, 71, 73, 75, 76, 77, 78, 79, 81, 91, 93, 94, 95, 97, 98, 100, 101, 102, 104, 105, 106, 107, 109, 110, 112, 114, 115, 117, 119, 122, 126, 128], "excluded_lines": [130, 131], "functions": {"UninterruptibleTestRunner.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [16, 17, 18], "excluded_lines": []}, "UninterruptibleTestRunner.signal_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [22, 23, 24, 25, 26, 27], "excluded_lines": []}, "UninterruptibleTestRunner.setup_signal_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [32], "excluded_lines": []}, "UninterruptibleTestRunner.restore_signal_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [36, 37], "excluded_lines": []}, "UninterruptibleTestRunner.run_tests": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [41, 42, 45, 62, 63, 64, 66, 67, 68, 69, 70, 71, 73, 75, 76, 77, 78, 79, 81, 91, 93, 94, 95, 97, 98, 100, 101, 102, 104, 105, 106, 107, 109, 110, 112], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [115, 117, 119, 122, 126, 128], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 2}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 20, 30, 34, 39, 114], "excluded_lines": [130, 131]}}, "classes": {"UninterruptibleTestRunner": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 47, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [16, 17, 18, 22, 23, 24, 25, 26, 27, 32, 36, 37, 41, 42, 45, 62, 63, 64, 66, 67, 68, 69, 70, 71, 73, 75, 76, 77, 78, 79, 81, 91, 93, 94, 95, 97, 98, 100, 101, 102, 104, 105, 106, 107, 109, 110, 112], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 2}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 20, 30, 34, 39, 114, 115, 117, 119, 122, 126, 128], "excluded_lines": [130, 131]}}}, "search_algorithms_new.py": {"executed_lines": [2, 5, 6, 7, 8, 9, 11, 13, 14, 15, 20, 22, 23, 24, 32, 33, 41, 55, 70, 80, 81, 103, 130, 186, 271, 322, 350, 356, 357, 371, 381, 397, 419, 423, 424, 438, 450, 471, 490, 495, 496, 510, 522, 540, 558, 595, 598, 606, 607, 621, 631, 647, 673], "summary": {"covered_lines": 42, "num_statements": 232, "percent_covered": 18.103448275862068, "percent_covered_display": "18.10", "missing_lines": 190, "excluded_lines": 46}, "missing_lines": [16, 17, 27, 28, 29, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 127, 128, 147, 148, 150, 152, 153, 155, 158, 159, 162, 163, 165, 167, 169, 172, 174, 176, 177, 179, 181, 182, 208, 211, 212, 213, 215, 217, 218, 220, 221, 222, 225, 226, 227, 228, 232, 233, 234, 236, 237, 239, 240, 242, 243, 246, 247, 248, 249, 253, 256, 257, 258, 259, 260, 265, 266, 267, 268, 269, 287, 288, 290, 291, 293, 294, 295, 296, 297, 298, 300, 301, 302, 305, 308, 309, 310, 311, 314, 316, 317, 320, 340, 341, 342, 344, 346, 347, 348, 351, 352, 353, 378, 379, 393, 394, 395, 410, 411, 412, 413, 414, 415, 416, 417, 420, 446, 447, 448, 462, 463, 464, 465, 466, 468, 469, 484, 485, 486, 487, 488, 491, 517, 518, 519, 520, 534, 535, 537, 538, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 586, 587, 588, 589, 590, 591, 592, 593, 596, 600, 601, 602, 603, 628, 629, 643, 644, 645, 666, 667, 668, 669, 671, 675], "excluded_lines": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77], "functions": {"SearchAlgorithm.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 12}, "missing_lines": [], "excluded_lines": [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]}, "SearchAlgorithm.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 13}, "missing_lines": [], "excluded_lines": [56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68]}, "SearchAlgorithm.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [71, 72, 73, 74, 75, 76, 77]}, "HashSetSearch.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 127, 128], "excluded_lines": []}, "HashSetSearch.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [147, 148, 150, 152, 153, 155, 158, 159, 162, 163, 165, 167, 169, 172, 174, 176, 177, 179, 181, 182], "excluded_lines": []}, "HashSetSearch.fast_load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [208, 211, 212, 213, 215, 217, 218, 220, 221, 222, 225, 226, 227, 228, 232, 233, 234, 236, 237, 239, 240, 242, 243, 246, 247, 248, 249, 253, 256, 257, 258, 259, 260, 265, 266, 267, 268, 269], "excluded_lines": []}, "HashSetSearch._simple_search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [287, 288, 290, 291, 293, 294, 295, 296, 297, 298, 300, 301, 302, 305, 308, 309, 310, 311, 314, 316, 317, 320], "excluded_lines": []}, "HashSetSearch.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [340, 341, 342, 344, 346, 347, 348], "excluded_lines": []}, "HashSetSearch.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [351, 352, 353], "excluded_lines": []}, "LinearSearch.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [378, 379], "excluded_lines": []}, "LinearSearch.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [393, 394, 395], "excluded_lines": []}, "LinearSearch.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [410, 411, 412, 413, 414, 415, 416, 417], "excluded_lines": []}, "LinearSearch.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [420], "excluded_lines": []}, "BinarySearch.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [446, 447, 448], "excluded_lines": []}, "BinarySearch.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [462, 463, 464, 465, 466, 468, 469], "excluded_lines": []}, "BinarySearch.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [484, 485, 486, 487, 488], "excluded_lines": []}, "BinarySearch.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [491], "excluded_lines": []}, "MMapSearch.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [517, 518, 519, 520], "excluded_lines": []}, "MMapSearch.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [534, 535, 537, 538], "excluded_lines": []}, "MMapSearch._build_line_index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [547, 548, 549, 550, 551, 552, 553, 554, 555, 556], "excluded_lines": []}, "MMapSearch.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 586, 587, 588, 589, 590, 591, 592, 593], "excluded_lines": []}, "MMapSearch.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [596], "excluded_lines": []}, "MMapSearch.__del__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [600, 601, 602, 603], "excluded_lines": []}, "GrepSearch.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [628, 629], "excluded_lines": []}, "GrepSearch.load": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [643, 644, 645], "excluded_lines": []}, "GrepSearch.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [666, 667, 668, 669, 671], "excluded_lines": []}, "GrepSearch.name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [675], "excluded_lines": []}, "": {"executed_lines": [2, 5, 6, 7, 8, 9, 11, 13, 14, 15, 20, 22, 23, 24, 32, 33, 41, 55, 70, 80, 81, 103, 130, 186, 271, 322, 350, 356, 357, 371, 381, 397, 419, 423, 424, 438, 450, 471, 490, 495, 496, 510, 522, 540, 558, 595, 598, 606, 607, 621, 631, 647, 673], "summary": {"covered_lines": 42, "num_statements": 47, "percent_covered": 89.36170212765957, "percent_covered_display": "89.36", "missing_lines": 5, "excluded_lines": 14}, "missing_lines": [16, 17, 27, 28, 29], "excluded_lines": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 54, 55, 69, 70]}}, "classes": {"SearchAlgorithm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 32}, "missing_lines": [], "excluded_lines": [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 73, 74, 75, 76, 77]}, "HashSetSearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 102, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 102, "excluded_lines": 0}, "missing_lines": [115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 127, 128, 147, 148, 150, 152, 153, 155, 158, 159, 162, 163, 165, 167, 169, 172, 174, 176, 177, 179, 181, 182, 208, 211, 212, 213, 215, 217, 218, 220, 221, 222, 225, 226, 227, 228, 232, 233, 234, 236, 237, 239, 240, 242, 243, 246, 247, 248, 249, 253, 256, 257, 258, 259, 260, 265, 266, 267, 268, 269, 287, 288, 290, 291, 293, 294, 295, 296, 297, 298, 300, 301, 302, 305, 308, 309, 310, 311, 314, 316, 317, 320, 340, 341, 342, 344, 346, 347, 348, 351, 352, 353], "excluded_lines": []}, "LinearSearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [378, 379, 393, 394, 395, 410, 411, 412, 413, 414, 415, 416, 417, 420], "excluded_lines": []}, "BinarySearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [446, 447, 448, 462, 463, 464, 465, 466, 468, 469, 484, 485, 486, 487, 488, 491], "excluded_lines": []}, "MMapSearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 42, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [517, 518, 519, 520, 534, 535, 537, 538, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 586, 587, 588, 589, 590, 591, 592, 593, 596, 600, 601, 602, 603], "excluded_lines": []}, "GrepSearch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [628, 629, 643, 644, 645, 666, 667, 668, 669, 671, 675], "excluded_lines": []}, "": {"executed_lines": [2, 5, 6, 7, 8, 9, 11, 13, 14, 15, 20, 22, 23, 24, 32, 33, 41, 55, 70, 80, 81, 103, 130, 186, 271, 322, 350, 356, 357, 371, 381, 397, 419, 423, 424, 438, 450, 471, 490, 495, 496, 510, 522, 540, 558, 595, 598, 606, 607, 621, 631, 647, 673], "summary": {"covered_lines": 42, "num_statements": 47, "percent_covered": 89.36170212765957, "percent_covered_display": "89.36", "missing_lines": 5, "excluded_lines": 14}, "missing_lines": [16, 17, 27, 28, 29], "excluded_lines": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 54, 55, 69, 70]}}}, "server.py": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 27, 28, 30, 36, 37, 44, 67, 75, 98, 130, 131, 138, 210, 211, 219, 221, 223, 275, 284, 291, 300, 385, 448], "summary": {"covered_lines": 27, "num_statements": 189, "percent_covered": 14.285714285714286, "percent_covered_display": "14.29", "missing_lines": 162, "excluded_lines": 2}, "missing_lines": [55, 56, 57, 59, 61, 64, 65, 69, 70, 72, 73, 88, 89, 91, 93, 94, 95, 106, 107, 110, 111, 112, 115, 118, 119, 120, 123, 149, 151, 154, 155, 156, 158, 161, 163, 164, 165, 168, 170, 171, 172, 173, 176, 177, 180, 183, 184, 187, 194, 195, 196, 197, 198, 201, 204, 205, 206, 207, 243, 244, 245, 246, 247, 250, 253, 256, 257, 258, 261, 262, 265, 268, 269, 271, 272, 273, 277, 278, 279, 280, 281, 282, 286, 287, 293, 294, 295, 296, 297, 313, 314, 316, 317, 318, 319, 322, 323, 324, 325, 326, 327, 329, 330, 332, 333, 335, 337, 342, 343, 344, 345, 347, 349, 352, 355, 358, 366, 367, 368, 369, 370, 372, 374, 376, 377, 378, 379, 386, 389, 390, 392, 394, 396, 397, 399, 400, 403, 404, 405, 408, 409, 410, 413, 414, 417, 418, 421, 426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 442, 443, 444, 445], "excluded_lines": [448, 449], "functions": {"FileSearchEngine.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 59, 61, 64, 65], "excluded_lines": []}, "FileSearchEngine._validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [69, 70, 72, 73], "excluded_lines": []}, "FileSearchEngine.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [88, 89, 91, 93, 94, 95], "excluded_lines": []}, "setup_logging": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [106, 107, 110, 111, 112, 115, 118, 119, 120, 123], "excluded_lines": []}, "StringSearchHandler.handle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [149, 151, 154, 155, 156, 158, 161, 163, 164, 165, 168, 170, 171, 172, 173, 176, 177, 180, 183, 184, 187, 194, 195, 196, 197, 198, 201, 204, 205, 206, 207], "excluded_lines": []}, "StringSearchServer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [243, 244, 245, 246, 247, 250, 253, 256, 257, 258, 261, 262, 265, 268, 269, 271, 272, 273], "excluded_lines": []}, "StringSearchServer.server_bind": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [277, 278, 279, 280, 281, 282], "excluded_lines": []}, "StringSearchServer.handle_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [286, 287], "excluded_lines": []}, "StringSearchServer.shutdown_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [293, 294, 295, 296, 297], "excluded_lines": []}, "create_ssl_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [313, 314, 316, 317, 318, 319, 322, 323, 324, 325, 326, 327, 329, 330, 332, 333, 335, 337, 342, 343, 344, 345, 347, 349, 352, 355, 358, 366, 367, 368, 369, 370, 372, 374, 376, 377, 378, 379], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [386, 389, 390, 392, 394, 396, 397, 399, 400, 403, 404, 405, 408, 409, 410, 413, 414, 417, 418, 421, 426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 442, 443, 444, 445], "excluded_lines": []}, "": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 27, 28, 30, 36, 37, 44, 67, 75, 98, 130, 131, 138, 210, 211, 219, 221, 223, 275, 284, 291, 300, 385, 448], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [448, 449]}}, "classes": {"FileSearchError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileSearchEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 59, 61, 64, 65, 69, 70, 72, 73, 88, 89, 91, 93, 94, 95], "excluded_lines": []}, "StringSearchHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [149, 151, 154, 155, 156, 158, 161, 163, 164, 165, 168, 170, 171, 172, 173, 176, 177, 180, 183, 184, 187, 194, 195, 196, 197, 198, 201, 204, 205, 206, 207], "excluded_lines": []}, "StringSearchServer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [243, 244, 245, 246, 247, 250, 253, 256, 257, 258, 261, 262, 265, 268, 269, 271, 272, 273, 277, 278, 279, 280, 281, 282, 286, 287, 293, 294, 295, 296, 297], "excluded_lines": []}, "": {"executed_lines": [2, 12, 13, 14, 15, 16, 17, 18, 19, 21, 27, 28, 30, 36, 37, 44, 67, 75, 98, 130, 131, 138, 210, 211, 219, 221, 223, 275, 284, 291, 300, 385, 448], "summary": {"covered_lines": 27, "num_statements": 110, "percent_covered": 24.545454545454547, "percent_covered_display": "24.55", "missing_lines": 83, "excluded_lines": 2}, "missing_lines": [106, 107, 110, 111, 112, 115, 118, 119, 120, 123, 313, 314, 316, 317, 318, 319, 322, 323, 324, 325, 326, 327, 329, 330, 332, 333, 335, 337, 342, 343, 344, 345, 347, 349, 352, 355, 358, 366, 367, 368, 369, 370, 372, 374, 376, 377, 378, 379, 386, 389, 390, 392, 394, 396, 397, 399, 400, 403, 404, 405, 408, 409, 410, 413, 414, 417, 418, 421, 426, 427, 428, 429, 431, 432, 435, 437, 438, 439, 440, 442, 443, 444, 445], "excluded_lines": [448, 449]}}}, "verify_installation.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 163, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 163, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 27, 29, 30, 33, 35, 37, 38, 40, 41, 43, 44, 46, 49, 51, 53, 62, 63, 64, 65, 66, 67, 68, 69, 71, 74, 76, 79, 91, 96, 98, 99, 100, 101, 102, 103, 104, 105, 107, 108, 109, 110, 111, 112, 113, 115, 118, 120, 122, 123, 124, 126, 127, 128, 129, 130, 132, 133, 134, 135, 138, 140, 142, 148, 149, 150, 151, 152, 153, 155, 156, 158, 161, 163, 165, 169, 170, 171, 172, 173, 174, 176, 179, 181, 183, 191, 192, 193, 194, 195, 196, 197, 198, 200, 201, 203, 206, 208, 210, 212, 213, 216, 219, 220, 223, 224, 225, 227, 228, 229, 230, 233, 235, 237, 239, 240, 241, 242, 243, 249, 250, 251, 252, 253, 254, 255, 257, 258, 260, 263, 265, 267, 268, 270, 271, 272, 273, 275, 276, 277, 279, 280, 281, 283, 284, 287, 289, 290, 291, 294, 307, 310, 311, 313], "excluded_lines": [316, 317], "functions": {"print_header": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [22, 23, 24], "excluded_lines": []}, "print_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [29, 30], "excluded_lines": []}, "check_python_version": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [35, 37, 38, 40, 41, 43, 44, 46], "excluded_lines": []}, "check_core_modules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [51, 53, 62, 63, 64, 65, 66, 67, 68, 69, 71], "excluded_lines": []}, "check_dependencies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [76, 79, 91, 96, 98, 99, 100, 101, 102, 103, 104, 105, 107, 108, 109, 110, 111, 112, 113, 115], "excluded_lines": []}, "check_configuration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [120, 122, 123, 124, 126, 127, 128, 129, 130, 132, 133, 134, 135], "excluded_lines": []}, "check_ssl_certificates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [140, 142, 148, 149, 150, 151, 152, 153, 155, 156, 158], "excluded_lines": []}, "check_test_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [163, 165, 169, 170, 171, 172, 173, 174, 176], "excluded_lines": []}, "check_executable_permissions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [181, 183, 191, 192, 193, 194, 195, 196, 197, 198, 200, 201, 203], "excluded_lines": []}, "run_basic_functionality_test": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [208, 210, 212, 213, 216, 219, 220, 223, 224, 225, 227, 228, 229, 230], "excluded_lines": []}, "run_type_checking": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [235, 237, 239, 240, 241, 242, 243, 249, 250, 251, 252, 253, 254, 255, 257, 258, 260], "excluded_lines": []}, "generate_summary_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [265, 267, 268, 270, 271, 272, 273, 275, 276, 277, 279, 280, 281, 283, 284], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 294, 307, 310, 311, 313], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 19, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 17, 20, 27, 33, 49, 74, 118, 138, 161, 179, 206, 233, 263, 287], "excluded_lines": [316, 317]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 163, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 163, "excluded_lines": 2}, "missing_lines": [12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 27, 29, 30, 33, 35, 37, 38, 40, 41, 43, 44, 46, 49, 51, 53, 62, 63, 64, 65, 66, 67, 68, 69, 71, 74, 76, 79, 91, 96, 98, 99, 100, 101, 102, 103, 104, 105, 107, 108, 109, 110, 111, 112, 113, 115, 118, 120, 122, 123, 124, 126, 127, 128, 129, 130, 132, 133, 134, 135, 138, 140, 142, 148, 149, 150, 151, 152, 153, 155, 156, 158, 161, 163, 165, 169, 170, 171, 172, 173, 174, 176, 179, 181, 183, 191, 192, 193, 194, 195, 196, 197, 198, 200, 201, 203, 206, 208, 210, 212, 213, 216, 219, 220, 223, 224, 225, 227, 228, 229, 230, 233, 235, 237, 239, 240, 241, 242, 243, 249, 250, 251, 252, 253, 254, 255, 257, 258, 260, 263, 265, 267, 268, 270, 271, 272, 273, 275, 276, 277, 279, 280, 281, 283, 284, 287, 289, 290, 291, 294, 307, 310, 311, 313], "excluded_lines": [316, 317]}}}}, "totals": {"covered_lines": 327, "num_statements": 1941, "percent_covered": 16.846986089644513, "percent_covered_display": "16.85", "missing_lines": 1614, "excluded_lines": 77}}
#!/usr/bin/env python3
"""
Create wrapper scripts for user-space installation.
"""

import os
from pathlib import Path

def main():
    home = Path.home()
    bin_dir = home / ".local" / "bin"
    install_dir = home / ".local" / "tcp-string-search"
    venv_dir = install_dir / "venv"
    
    # Create bin directory
    bin_dir.mkdir(parents=True, exist_ok=True)
    
    # Wrapper scripts
    scripts = {
        "tcp-string-search-server": f"""#!/bin/bash
cd "{install_dir}"
source "{venv_dir}/bin/activate"
exec python3 server.py "$@"
""",
        "tcp-string-search-client": f"""#!/bin/bash
cd "{install_dir}"
source "{venv_dir}/bin/activate"
exec python3 client.py "$@"
""",
        "tcp-string-search-perf": f"""#!/bin/bash
cd "{install_dir}"
source "{venv_dir}/bin/activate"
exec python3 performance_test_client.py "$@"
""",
        "tcp-string-search-gencerts": f"""#!/bin/bash
cd "{install_dir}"
source "{venv_dir}/bin/activate"
exec python3 generate_certs.py "$@"
"""
    }
    
    print("Creating wrapper scripts...")
    for name, content in scripts.items():
        script_path = bin_dir / name
        with open(script_path, 'w') as f:
            f.write(content)
        os.chmod(script_path, 0o755)
        print(f"✅ Created: {script_path}")
    
    print("\nTesting user-space server...")
    # Test if we can start the server
    try:
        import subprocess
        import time
        
        # Start server in background
        server_process = subprocess.Popen(
            [str(bin_dir / "tcp-string-search-server"), "--help"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        stdout, stderr = server_process.communicate(timeout=10)
        
        if server_process.returncode == 0:
            print("✅ Server wrapper script works")
        else:
            print(f"❌ Server wrapper failed: {stderr.decode()}")
            
    except Exception as e:
        print(f"❌ Server test failed: {e}")
    
    print("\nTesting client...")
    try:
        # Test client help
        client_process = subprocess.Popen(
            [str(bin_dir / "tcp-string-search-client"), "--help"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        stdout, stderr = client_process.communicate(timeout=10)
        
        if client_process.returncode == 0:
            print("✅ Client wrapper script works")
        else:
            print(f"❌ Client wrapper failed: {stderr.decode()}")
            
    except Exception as e:
        print(f"❌ Client test failed: {e}")
    
    print(f"\n🎉 Wrapper scripts created successfully!")
    print(f"📁 Location: {bin_dir}")
    print(f"🔧 Available commands:")
    for name in scripts.keys():
        print(f"  - {name}")

if __name__ == "__main__":
    main()

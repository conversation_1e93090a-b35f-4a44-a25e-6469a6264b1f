# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |   50K |   100K |   250K |
|:------------------------------|------:|------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.001 |   nan |    nan |    nan |
| Hash Set (FrozenSet)          | 0     |   nan |    nan |    nan |
| Hash Set (FrozenSet) (Reread) | 0.065 |   nan |    nan |    nan |
| Linear Search (Optimized)     | 0.019 |   nan |    nan |    nan |
| Memory-Mapped                 | 0.012 |   nan |    nan |    nan |
| Native Grep                   | 1.333 |   nan |    nan |    nan |

### Load Time by File Size

| Algorithm                     |   10K |   50K |   100K |   250K |
|:------------------------------|------:|------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.068 |   nan |    nan |    nan |
| Hash Set (FrozenSet)          | 0.107 |   nan |    nan |    nan |
| Hash Set (FrozenSet) (Reread) | 0.083 |   nan |    nan |    nan |
| Linear Search (Optimized)     | 0.001 |   nan |    nan |    nan |
| Memory-Mapped                 | 0.019 |   nan |    nan |    nan |
| Native Grep                   | 0.001 |   nan |    nan |    nan |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization

#!/bin/bash

# TCP String Search Server - Service Installation Script
# Author: <PERSON>
# Date: 2025

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="tcp-string-search"
SERVICE_USER="tcp-search"
SERVICE_GROUP="tcp-search"
INSTALL_DIR="$HOME/.local/tcp-string-search"
SERVICE_FILE="tcp-string-search.service"
CURRENT_DIR="$(pwd)"

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo <PERSON>e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python 3
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check systemctl
    if ! command -v systemctl &> /dev/null; then
        print_error "systemctl is required (systemd-based system)"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

create_user() {
    print_status "Creating service user and group..."
    
    # Create group if it doesn't exist
    if ! getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupadd --system "$SERVICE_GROUP"
        print_success "Created group: $SERVICE_GROUP"
    else
        print_warning "Group $SERVICE_GROUP already exists"
    fi
    
    # Create user if it doesn't exist
    if ! getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        useradd --system --gid "$SERVICE_GROUP" --home-dir "$INSTALL_DIR" \
                --shell /bin/false --comment "TCP String Search Service" "$SERVICE_USER"
        print_success "Created user: $SERVICE_USER"
    else
        print_warning "User $SERVICE_USER already exists"
    fi
}

install_files() {
    print_status "Installing application files..."
    
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    
    # Copy application files
    cp -r "$CURRENT_DIR"/* "$INSTALL_DIR/"
    
    # Create logs directory
    mkdir -p "$INSTALL_DIR/logs"
    
    # Set ownership
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    
    # Set permissions
    chmod 755 "$INSTALL_DIR"
    chmod 644 "$INSTALL_DIR"/*.py
    chmod 755 "$INSTALL_DIR/server.py"
    chmod 755 "$INSTALL_DIR/client.py"
    chmod 644 "$INSTALL_DIR/config.ini"
    chmod 755 "$INSTALL_DIR/logs"
    
    # Set SSL certificate permissions if they exist
    if [ -d "$INSTALL_DIR/certs" ]; then
        chmod 700 "$INSTALL_DIR/certs"
        chmod 600 "$INSTALL_DIR/certs"/*
        chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR/certs"
    fi
    
    print_success "Application files installed to $INSTALL_DIR"
}

install_systemd_service() {
    print_status "Installing systemd service..."
    
    # Copy service file
    cp "$CURRENT_DIR/$SERVICE_FILE" "/etc/systemd/system/"
    
    # Set permissions
    chmod 644 "/etc/systemd/system/$SERVICE_FILE"
    
    # Reload systemd
    systemctl daemon-reload
    
    print_success "Systemd service installed"
}

install_dependencies() {
    print_status "Installing Python dependencies..."
    
    # Install pip if not present
    if ! command -v pip3 &> /dev/null; then
        print_warning "pip3 not found, installing..."
        apt-get update
        apt-get install -y python3-pip
    fi
    
    # Install requirements
    cd "$INSTALL_DIR"
    pip3 install -r requirements.txt
    
    print_success "Python dependencies installed"
}

configure_firewall() {
    print_status "Configuring firewall (if ufw is active)..."
    
    if command -v ufw &> /dev/null && ufw status | grep -q "Status: active"; then
        # Get port from config
        PORT=$(grep -E "^port\s*=" "$INSTALL_DIR/config.ini" | cut -d'=' -f2 | tr -d ' ')
        if [ -n "$PORT" ]; then
            ufw allow "$PORT/tcp" comment "TCP String Search Server"
            print_success "Firewall rule added for port $PORT"
        else
            print_warning "Could not determine port from config.ini"
        fi
    else
        print_warning "ufw not active or not installed, skipping firewall configuration"
    fi
}

generate_certificates() {
    print_status "Generating SSL certificates if needed..."
    
    if [ ! -f "$INSTALL_DIR/certs/server.crt" ]; then
        cd "$INSTALL_DIR"
        if [ -f "generate_certs.py" ]; then
            sudo -u "$SERVICE_USER" python3 generate_certs.py
            print_success "SSL certificates generated"
        else
            print_warning "generate_certs.py not found, skipping certificate generation"
        fi
    else
        print_warning "SSL certificates already exist"
    fi
}

main() {
    echo "TCP String Search Server - Service Installation"
    echo "=============================================="
    echo
    
    check_root
    check_dependencies
    create_user
    install_files
    install_systemd_service
    install_dependencies
    configure_firewall
    generate_certificates
    
    echo
    print_success "Installation completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Edit configuration: sudo nano $INSTALL_DIR/config.ini"
    echo "2. Start the service: sudo systemctl start $SERVICE_NAME"
    echo "3. Enable auto-start: sudo systemctl enable $SERVICE_NAME"
    echo "4. Check status: sudo systemctl status $SERVICE_NAME"
    echo "5. View logs: sudo journalctl -u $SERVICE_NAME -f"
    echo
    echo "Service commands:"
    echo "  Start:   sudo systemctl start $SERVICE_NAME"
    echo "  Stop:    sudo systemctl stop $SERVICE_NAME"
    echo "  Restart: sudo systemctl restart $SERVICE_NAME"
    echo "  Status:  sudo systemctl status $SERVICE_NAME"
    echo "  Logs:    sudo journalctl -u $SERVICE_NAME"
    echo
}

# Handle script interruption
trap 'print_error "Installation interrupted"; exit 1' INT TERM

# Run main function
main "$@"

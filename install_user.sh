#!/bin/bash

# TCP String Search Server - User-Space Installation Script
# Author: <PERSON>
# Date: 2025
# 
# This script installs the server in user space without requiring sudo privileges.
# Suitable for containers and environments where sudo is restricted.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="tcp-string-search"
INSTALL_DIR="$HOME/.local/tcp-string-search"
CURRENT_DIR="$(pwd)"
VENV_DIR="$INSTALL_DIR/venv"

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python 3
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 is required but not installed"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

create_directories() {
    print_status "Creating installation directories..."
    
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$INSTALL_DIR/logs"
    mkdir -p "$HOME/.local/bin"
    
    print_success "Directories created"
}

install_files() {
    print_status "Installing application files..."
    
    # Copy application files
    cp -r "$CURRENT_DIR"/* "$INSTALL_DIR/"
    
    # Remove unnecessary files
    rm -rf "$INSTALL_DIR/.git" 2>/dev/null || true
    rm -rf "$INSTALL_DIR/__pycache__" 2>/dev/null || true
    rm -rf "$INSTALL_DIR/.pytest_cache" 2>/dev/null || true
    rm -rf "$INSTALL_DIR/.mypy_cache" 2>/dev/null || true
    rm -rf "$INSTALL_DIR/htmlcov" 2>/dev/null || true
    
    # Set permissions
    chmod 755 "$INSTALL_DIR"
    chmod 644 "$INSTALL_DIR"/*.py
    chmod 755 "$INSTALL_DIR/server.py"
    chmod 755 "$INSTALL_DIR/client.py"
    chmod 755 "$INSTALL_DIR/generate_certs.py"
    chmod 644 "$INSTALL_DIR/config.ini"
    chmod 755 "$INSTALL_DIR/logs"
    
    print_success "Application files installed to $INSTALL_DIR"
}

create_virtual_environment() {
    print_status "Creating Python virtual environment..."
    
    # Create virtual environment
    python3 -m venv "$VENV_DIR"
    
    # Activate virtual environment and install dependencies
    source "$VENV_DIR/bin/activate"
    pip install --upgrade pip
    pip install -r "$INSTALL_DIR/requirements.txt"
    
    print_success "Virtual environment created and dependencies installed"
}

create_wrapper_scripts() {
    print_status "Creating wrapper scripts..."
    
    # Create server wrapper script
    cat > "$HOME/.local/bin/tcp-string-search-server" << EOF
#!/bin/bash
# TCP String Search Server Wrapper Script
cd "$INSTALL_DIR"
source "$VENV_DIR/bin/activate"
exec python3 server.py "\$@"
EOF
    chmod +x "$HOME/.local/bin/tcp-string-search-server"
    
    # Create client wrapper script
    cat > "$HOME/.local/bin/tcp-string-search-client" << EOF
#!/bin/bash
# TCP String Search Client Wrapper Script
cd "$INSTALL_DIR"
source "$VENV_DIR/bin/activate"
exec python3 client.py "\$@"
EOF
    chmod +x "$HOME/.local/bin/tcp-string-search-client"
    
    # Create performance test wrapper script
    cat > "$HOME/.local/bin/tcp-string-search-perf" << EOF
#!/bin/bash
# TCP String Search Performance Test Wrapper Script
cd "$INSTALL_DIR"
source "$VENV_DIR/bin/activate"
exec python3 performance_test_client.py "\$@"
EOF
    chmod +x "$HOME/.local/bin/tcp-string-search-perf"
    
    # Create certificate generation wrapper script
    cat > "$HOME/.local/bin/tcp-string-search-gencerts" << EOF
#!/bin/bash
# TCP String Search Certificate Generation Wrapper Script
cd "$INSTALL_DIR"
source "$VENV_DIR/bin/activate"
exec python3 generate_certs.py "\$@"
EOF
    chmod +x "$HOME/.local/bin/tcp-string-search-gencerts"
    
    print_success "Wrapper scripts created in $HOME/.local/bin"
}

generate_certificates() {
    print_status "Generating SSL certificates if needed..."
    
    if [ ! -f "$INSTALL_DIR/certs/server.crt" ]; then
        cd "$INSTALL_DIR"
        source "$VENV_DIR/bin/activate"
        if [ -f "generate_certs.py" ]; then
            python3 generate_certs.py
            print_success "SSL certificates generated"
        else
            print_warning "generate_certs.py not found, skipping certificate generation"
        fi
    else
        print_warning "SSL certificates already exist"
    fi
}

update_config() {
    print_status "Updating configuration for user installation..."
    
    # Update config.ini to use absolute paths
    sed -i "s|test_data/200k.txt|$INSTALL_DIR/test_data/200k.txt|g" "$INSTALL_DIR/config.ini"
    sed -i "s|logs/server.log|$INSTALL_DIR/logs/server.log|g" "$INSTALL_DIR/config.ini"
    sed -i "s|certs/|$INSTALL_DIR/certs/|g" "$INSTALL_DIR/config.ini"
    
    print_success "Configuration updated"
}

create_systemd_user_service() {
    print_status "Creating systemd user service..."
    
    # Create systemd user directory
    mkdir -p "$HOME/.config/systemd/user"
    
    # Create user service file
    cat > "$HOME/.config/systemd/user/tcp-string-search.service" << EOF
[Unit]
Description=TCP String Search Server (User Service)
Documentation=file://$INSTALL_DIR/README.md
After=network.target
Wants=network.target

[Service]
Type=simple
WorkingDirectory=$INSTALL_DIR
ExecStart=$VENV_DIR/bin/python $INSTALL_DIR/server.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# Environment
Environment=PYTHONPATH=$INSTALL_DIR
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=default.target
EOF
    
    # Reload systemd user daemon
    systemctl --user daemon-reload
    
    print_success "Systemd user service created"
}

add_to_path() {
    print_status "Adding to PATH..."
    
    # Add to .bashrc if not already present
    if ! grep -q "$HOME/.local/bin" "$HOME/.bashrc" 2>/dev/null; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
        print_success "Added $HOME/.local/bin to PATH in .bashrc"
    else
        print_warning "$HOME/.local/bin already in PATH"
    fi
    
    # Add to current session
    export PATH="$HOME/.local/bin:$PATH"
}

main() {
    echo "TCP String Search Server - User-Space Installation"
    echo "================================================="
    echo
    
    check_dependencies
    create_directories
    install_files
    create_virtual_environment
    create_wrapper_scripts
    update_config
    generate_certificates
    create_systemd_user_service
    add_to_path
    
    echo
    print_success "User-space installation completed successfully!"
    echo
    echo "Installation directory: $INSTALL_DIR"
    echo "Virtual environment: $VENV_DIR"
    echo
    echo "Available commands (after restarting shell or running 'source ~/.bashrc'):"
    echo "  tcp-string-search-server    - Start the server"
    echo "  tcp-string-search-client    - Run the client"
    echo "  tcp-string-search-perf      - Run performance tests"
    echo "  tcp-string-search-gencerts  - Generate SSL certificates"
    echo
    echo "Systemd user service commands:"
    echo "  systemctl --user start tcp-string-search     - Start service"
    echo "  systemctl --user stop tcp-string-search      - Stop service"
    echo "  systemctl --user enable tcp-string-search    - Enable auto-start"
    echo "  systemctl --user status tcp-string-search    - Check status"
    echo "  journalctl --user -u tcp-string-search -f    - View logs"
    echo
    echo "Configuration file: $INSTALL_DIR/config.ini"
    echo "Log files: $INSTALL_DIR/logs/"
    echo
    echo "To start the server manually:"
    echo "  cd $INSTALL_DIR && source $VENV_DIR/bin/activate && python3 server.py"
    echo
}

# Handle script interruption
trap 'print_error "Installation interrupted"; exit 1' INT TERM

# Run main function
main "$@"

#!/bin/bash

# Complete Test Suite Runner with Coverage
# This script runs all tests with comprehensive coverage reporting
# and is immune to keyboard interrupts

set -e  # Exit on error

# Trap signals to prevent interruption
trap 'echo "🛡️  KeyboardInterrupt blocked - tests continuing in background"; echo "📊 Monitor with: tail -f $LOG_FILE"' INT TERM

# Function to ignore signals during critical operations
ignore_signals() {
    trap '' INT TERM
}

# Function to restore signal handling
restore_signals() {
    trap 'echo "🛡️  KeyboardInterrupt blocked - tests continuing in background"' INT TERM
}

# Configuration
PROJECT_DIR="/home/<USER>/CODES/interview"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="test_results_${TIMESTAMP}.log"
PID_FILE="pytest_${TIMESTAMP}.pid"

echo "Starting comprehensive test suite with coverage..."
echo "Project directory: $PROJECT_DIR"
echo "Log file: $LOG_FILE"
echo "PID file: $PID_FILE"

# Change to project directory
cd "$PROJECT_DIR"

# Clean previous coverage data
echo "Cleaning previous coverage data..."
rm -rf htmlcov/ coverage.xml coverage.json .coverage

# Create coverage configuration if it doesn't exist
if [ ! -f .coveragerc ]; then
    echo "Creating .coveragerc configuration..."
    cat > .coveragerc << 'EOF'
[run]
source = .
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    setup.py
    conftest.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
skip_covered = False
precision = 2

[html]
directory = htmlcov
title = TCP String Search Server Coverage Report

[xml]
output = coverage.xml

[json]
output = coverage.json
EOF
fi

# Function to run tests with signal protection
run_tests() {
    ignore_signals  # Block signals during test execution

    echo "🛡️  Starting protected test execution..."
    echo "⚠️  KeyboardInterrupt protection is ACTIVE"
    echo "📊 Monitor progress: tail -f $LOG_FILE"

    # Use setsid to create new process group immune to parent signals
    setsid bash -c "
        PYTHONPATH=. python3 -m pytest \
            --cov=. \
            --cov-report=html:htmlcov \
            --cov-report=term-missing \
            --cov-report=xml:coverage.xml \
            --cov-report=json:coverage.json \
            --cov-config=.coveragerc \
            --tb=short \
            --maxfail=0 \
            --continue-on-collection-errors \
            --disable-warnings \
            -v \
            --durations=10 \
            --strict-markers \
            --strict-config \
            --interrupt-protection
    "

    restore_signals  # Restore signal handling after tests
}

# Run tests in background with timeout protection
echo "Starting test execution..."
timeout 7200 bash -c "$(declare -f run_tests); run_tests" > "$LOG_FILE" 2>&1 &

# Save PID
TEST_PID=$!
echo $TEST_PID > "$PID_FILE"

echo "Test suite started with PID: $TEST_PID"
echo "Log file: $LOG_FILE"
echo "To monitor progress: tail -f $LOG_FILE"
echo "To check if running: ps -p $TEST_PID"
echo "To kill if needed: kill $TEST_PID"

# Wait for completion
wait $TEST_PID
EXIT_CODE=$?

echo "Test suite completed with exit code: $EXIT_CODE"

# Generate summary
echo "Generating test summary..."
echo "==================== TEST SUMMARY ====================" >> "$LOG_FILE"
echo "Completion time: $(date)" >> "$LOG_FILE"
echo "Exit code: $EXIT_CODE" >> "$LOG_FILE"

# Check if coverage files were generated
if [ -f coverage.xml ]; then
    echo "Coverage XML report: coverage.xml" >> "$LOG_FILE"
fi

if [ -d htmlcov ]; then
    echo "Coverage HTML report: htmlcov/index.html" >> "$LOG_FILE"
fi

if [ -f coverage.json ]; then
    echo "Coverage JSON report: coverage.json" >> "$LOG_FILE"
fi

echo "Log file: $LOG_FILE" >> "$LOG_FILE"
echo "=======================================================" >> "$LOG_FILE"

# Clean up PID file
rm -f "$PID_FILE"

echo "Test execution completed. Check $LOG_FILE for results."
echo "Coverage reports available in htmlcov/ directory."

exit $EXIT_CODE

#!/bin/bash

# Docker-based Test Runner - Ultimate Interruption Protection
# Runs tests in isolated container immune to host interrupts

set -e

PROJECT_DIR="/home/<USER>/CODES/interview"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
CONTAINER_NAME="pytest_runner_${TIMESTAMP}"

echo "🐳 Starting Docker-based test execution..."
echo "📦 Container: $CONTAINER_NAME"
echo "🛡️  Complete isolation from host interrupts"

# Create Dockerfile for test environment
cat > Dockerfile.pytest << 'EOF'
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional test dependencies
RUN pip install pytest-cov pytest-mock pytest-xdist

# Copy project files
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTEST_DISABLE_PLUGIN_AUTOLOAD=1

# Default command
CMD ["python", "-m", "pytest", \
     "--cov=.", \
     "--cov-report=html:htmlcov", \
     "--cov-report=term-missing", \
     "--cov-report=xml:coverage.xml", \
     "--tb=short", \
     "--maxfail=0", \
     "--continue-on-collection-errors", \
     "--disable-warnings", \
     "-v"]
EOF

# Build Docker image
echo "🔨 Building Docker image..."
docker build -f Dockerfile.pytest -t pytest-runner:latest .

# Run tests in container with volume mounts for results
echo "🚀 Running tests in isolated container..."
docker run \
    --name "$CONTAINER_NAME" \
    --rm \
    -v "$(pwd)/htmlcov:/app/htmlcov" \
    -v "$(pwd)/coverage.xml:/app/coverage.xml" \
    -v "$(pwd)/test_results_${TIMESTAMP}.log:/app/test_results.log" \
    pytest-runner:latest \
    bash -c "
        echo '🛡️  Test execution starting in isolated container...'
        echo '⏰ Start time: \$(date)'
        python -m pytest \
            --cov=. \
            --cov-report=html:htmlcov \
            --cov-report=term-missing \
            --cov-report=xml:coverage.xml \
            --tb=short \
            --maxfail=0 \
            --continue-on-collection-errors \
            --disable-warnings \
            -v \
            --durations=10 2>&1 | tee test_results.log
        echo '✅ Test execution completed at: \$(date)'
    "

echo "✅ Docker-based test execution completed!"
echo "📊 Results available in htmlcov/ and coverage.xml"
echo "📝 Log: test_results_${TIMESTAMP}.log"

# Cleanup
rm -f Dockerfile.pytest

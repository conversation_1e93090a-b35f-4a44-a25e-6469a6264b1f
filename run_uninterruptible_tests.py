#!/usr/bin/env python3
"""
Uninterruptible Test Runner
Runs pytest with signal handling to prevent KeyboardInterrupt
"""

import signal
import sys
import subprocess
import os
import time
from datetime import datetime

class UninterruptibleTestRunner:
    def __init__(self):
        self.original_sigint_handler = None
        self.test_process = None
        self.interrupted = False
        
    def signal_handler(self, signum, frame):
        """Handle SIGINT (Ctrl+C) gracefully"""
        if signum == signal.SIGINT:
            print(f"\n⚠️  KeyboardInterrupt received at {datetime.now()}")
            print("🛡️  Test execution is protected and will continue...")
            print("📊 To monitor progress: tail -f test_results_*.log")
            print("🔄 Tests will complete in background")
            self.interrupted = True
            # Don't actually interrupt - just log it
            
    def setup_signal_handling(self):
        """Setup signal handling to ignore interrupts"""
        self.original_sigint_handler = signal.signal(signal.SIGINT, self.signal_handler)
        
    def restore_signal_handling(self):
        """Restore original signal handling"""
        if self.original_sigint_handler:
            signal.signal(signal.SIGINT, self.original_sigint_handler)
            
    def run_tests(self):
        """Run pytest with comprehensive coverage"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"test_results_{timestamp}.log"
        
        # Pytest command with all coverage options
        cmd = [
            sys.executable, "-m", "pytest",
            "--cov=.",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml:coverage.xml",
            "--cov-report=json:coverage.json",
            "--tb=short",
            "--maxfail=0",  # Continue on failures
            "--continue-on-collection-errors",
            "--disable-warnings",
            "-v",
            "--durations=10",
            "--no-cov-on-fail",  # Still generate coverage on test failures
        ]
        
        # Set environment
        env = os.environ.copy()
        env["PYTHONPATH"] = "."
        env["PYTEST_DISABLE_PLUGIN_AUTOLOAD"] = "1"  # Prevent plugin conflicts
        
        print(f"🚀 Starting uninterruptible test run...")
        print(f"📝 Log file: {log_file}")
        print(f"⏰ Started at: {datetime.now()}")
        print(f"🛡️  Protected against KeyboardInterrupt")
        print(f"📊 Monitor with: tail -f {log_file}")
        print("-" * 60)
        
        try:
            # Run tests with output redirection
            with open(log_file, 'w') as f:
                f.write(f"Test run started at: {datetime.now()}\n")
                f.write(f"Command: {' '.join(cmd)}\n")
                f.write("-" * 60 + "\n")
                f.flush()
                
                self.test_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    env=env,
                    cwd=os.getcwd(),
                    preexec_fn=os.setsid  # Create new process group
                )
                
                # Wait for completion
                return_code = self.test_process.wait()
                
                f.write(f"\n{'-' * 60}\n")
                f.write(f"Test run completed at: {datetime.now()}\n")
                f.write(f"Exit code: {return_code}\n")
                
                if self.interrupted:
                    f.write("⚠️  KeyboardInterrupt was received but ignored\n")
                    
        except Exception as e:
            print(f"❌ Error running tests: {e}")
            return 1
            
        print(f"\n✅ Test run completed!")
        print(f"📝 Results in: {log_file}")
        print(f"📊 Coverage HTML: htmlcov/index.html")
        print(f"📈 Coverage XML: coverage.xml")
        
        if self.interrupted:
            print("⚠️  Note: KeyboardInterrupt was received but tests completed successfully")
            
        return return_code

def main():
    runner = UninterruptibleTestRunner()
    
    try:
        # Setup signal protection
        runner.setup_signal_handling()
        
        # Run tests
        exit_code = runner.run_tests()
        
    finally:
        # Always restore signal handling
        runner.restore_signal_handling()
        
    sys.exit(exit_code)

if __name__ == "__main__":
    main()

[Unit]
Description=TCP String Search Server
Documentation=file://%h/.local/tcp-string-search/README.md
After=network.target
Wants=network.target

[Service]
Type=simple
User=tcp-search
Group=tcp-search
WorkingDirectory=%h/.local/tcp-string-search
ExecStart=/usr/bin/python3 %h/.local/tcp-string-search/server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=tcp-string-search

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=%h/.local/tcp-string-search/logs
ReadOnlyPaths=%h/.local/tcp-string-search

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment
Environment=PYTHONPATH=%h/.local/tcp-string-search
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target

============================= test session starts ==============================
platform linux -- Python 3.10.12, pytest-8.3.5, pluggy-1.6.0 -- /usr/bin/python3
cachedir: .pytest_cache
rootdir: /home/<USER>/CODES/interview
plugins: mock-3.14.1, cov-4.1.0, forked-1.4.0, anyio-3.5.0, xdist-2.5.0, sugar-0.9.4
collecting ... collected 332 items

tests/test_client.py::TestSearchClient::test_client_initialization PASSED [  0%]
tests/test_client.py::TestSearchClient::test_client_initialization_with_custom_params PASSED [  0%]
tests/test_client.py::TestSearchClient::test_create_ssl_context_default PASSED [  0%]
tests/test_client.py::TestSearchClient::test_create_ssl_context_with_ca_file PASSED [  1%]
tests/test_client.py::TestSearchClient::test_create_ssl_context_no_verify PASSED [  1%]
tests/test_client.py::TestSearchClient::test_create_ssl_context_with_client_cert PASSED [  1%]
tests/test_client.py::TestSearchClient::test_search_successful_non_ssl PASSED [  2%]
tests/test_client.py::TestSearchClient::test_search_successful_with_ssl PASSED [  2%]
tests/test_client.py::TestSearchClient::test_search_query_too_long PASSED [  2%]
tests/test_client.py::TestSearchClient::test_search_connection_timeout PASSED [  3%]
tests/test_client.py::TestSearchClient::test_search_connection_error PASSED [  3%]
tests/test_client.py::TestSearchClient::test_search_socket_cleanup_on_exception PASSED [  3%]
tests/test_client.py::TestSearchClient::test_test_connection_success PASSED [  3%]
tests/test_client.py::TestSearchClient::test_test_connection_failure PASSED [  4%]
tests/test_client.py::TestSearchClient::test_search_unicode_query PASSED [  4%]
tests/test_client.py::TestSearchClient::test_search_empty_response PASSED [  4%]
tests/test_client.py::TestSearchClient::test_search_response_with_whitespace PASSED [  5%]
tests/test_client.py::TestSearchClient::test_search_timing_accuracy PASSED [  5%]
tests/test_client.py::TestMainFunction::test_main_test_connection_success PASSED [  5%]
tests/test_client.py::TestMainFunction::test_main_test_connection_failure PASSED [  6%]
tests/test_client.py::TestMainFunction::test_main_single_query_success PASSED [  6%]
tests/test_client.py::TestMainFunction::test_main_single_query_error PASSED [  6%]
tests/test_client.py::TestMainFunction::test_main_interactive_mode PASSED [  6%]
tests/test_client.py::TestMainFunction::test_main_interactive_keyboard_interrupt 

---------- coverage: platform linux, python 3.10.12-final-0 ----------
Name                             Stmts   Miss   Cover   Missing
---------------------------------------------------------------
__init__.py                          0      0 100.00%
client.py                          201     90  55.22%   65-74, 87-89, 93, 99-119, 123-128, 132-162, 179, 236-277, 311-313, 393-396
config_loader.py                   124     45  63.71%   73-78, 101, 123, 129-130, 142-153, 158, 164, 166, 170, 174, 176, 187-192, 200, 203, 209, 231, 250, 265-276
count_and_run_tests.py              55     55   0.00%   6-99
create_wrappers.py                  41     41   0.00%   6-96
generate_certs.py                  147    147   0.00%   12-330
generate_performance_report.py     335    301  10.15%   37-40, 54-61, 65-125, 129-149, 153-184, 188-219, 223-251, 255-341, 345-350, 354-523, 527-570, 575-602
generate_speed_report.py           119    104  12.61%   75-98, 127-141, 163-202, 221-320
performance_test_client.py         185    166  10.27%   45-49, 53-63, 72-96, 109-182, 200-316, 323-327, 332, 360-486
run_comprehensive_tests.py          84     84   0.00%   12-159
search_algorithms_new.py           232    190  18.10%   16-17, 27-29, 115-128, 147-182, 208-269, 287-320, 340-348, 351-353, 378-379, 393-395, 410-417, 420, 446-448, 462-469, 484-488, 491, 517-520, 534-538, 547-556, 574-593, 596, 600-603, 628-629, 643-645, 666-671, 675
server.py                          189    162  14.29%   55-65, 69-73, 88-95, 106-123, 149-207, 243-273, 277-282, 286-287, 293-297, 313-379, 386-445
verify_installation.py             163    163   0.00%   12-313
---------------------------------------------------------------
TOTAL                             1875   1548  17.44%
Coverage HTML written to dir htmlcov
Coverage XML written to file coverage.xml
Coverage JSON written to file coverage.json


============================= slowest 10 durations =============================
0.01s call     tests/test_client.py::TestSearchClient::test_search_timing_accuracy
0.01s call     tests/test_client.py::TestMainFunction::test_main_test_connection_failure

(8 durations < 0.005s hidden.  Use -vv to show these durations.)
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! KeyboardInterrupt !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
/usr/lib/python3.10/unittest/mock.py:1173: KeyboardInterrupt
(to show a full traceback on KeyboardInterrupt use --full-trace)
============================== 23 passed in 2.51s ==============================

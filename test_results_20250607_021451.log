Test run started at: 2025-06-07 02:14:51.381421
Command: /usr/bin/python3 -m pytest --cov=. --cov-report=html:htmlcov --cov-report=term-missing --cov-report=xml:coverage.xml --cov-report=json:coverage.json --tb=short --maxfail=0 --continue-on-collection-errors --disable-warnings -v --durations=10 --no-cov-on-fail
------------------------------------------------------------
ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]
__main__.py: error: unrecognized arguments: --cov=. --cov-report=html:htmlcov --cov-report=term-missing --cov-report=xml:coverage.xml --cov-report=json:coverage.json --no-cov-on-fail
  inifile: None
  rootdir: /home/<USER>/CODES/interview


------------------------------------------------------------
Test run completed at: 2025-06-07 02:14:51.578965
Exit code: 4

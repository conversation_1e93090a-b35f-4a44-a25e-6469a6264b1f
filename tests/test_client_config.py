#!/usr/bin/env python3
"""
Test client configuration loading
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

try:
    from client import load_config
    
    print("Testing client configuration loading...")
    print("=" * 50)
    
    config = load_config()
    print("Configuration loaded:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\nChecking certificate files:")
    cert_files = ['ssl_cert_file', 'ssl_key_file', 'ca_file']
    for cert_key in cert_files:
        if cert_key in config:
            cert_path = config[cert_key]
            exists = os.path.exists(cert_path) if cert_path else False
            print(f"  {cert_key}: {cert_path} (exists: {exists})")
    
    print("\nTesting simple connection...")
    import socket
    
    # Test if port 8888 is open
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 8888))
        sock.close()
        if result == 0:
            print("  Port 8888 is open")
        else:
            print(f"  Port 8888 is closed (error: {result})")
    except Exception as e:
        print(f"  Port test failed: {e}")
    
    # Test raw TCP connection
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('localhost', 8888))
        sock.send(b'test\n')
        response = sock.recv(1024)
        sock.close()
        print(f"  Raw TCP response: {response}")
    except Exception as e:
        print(f"  Raw TCP test failed: {e}")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
"""
Comprehensive Test Suite for config_loader Module

This module provides exhaustive testing for all functions and classes
in the config_loader module, ensuring 100% code coverage and robust
error handling.

Test Coverage:
- load_config function with all parameter combinations
- get_file_path function with various scenarios
- find_config_file function with different search paths
- ConfigurationError exception handling
- Type validation for all configuration sections
- Edge cases and boundary conditions
- File system permission scenarios
- Invalid configuration formats

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, mock_open

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from config_loader import (
    load_config, get_file_path, find_config_file, ConfigurationError,
    DefaultConfig, ServerConfig, SSLConfig, LoggingConfig
)


class TestLoadConfigFunction:
    """Comprehensive tests for load_config function."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def create_valid_config(self, temp_dir: Path, **overrides) -> Path:
        """Create a valid configuration file with optional overrides."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        defaults = {
            'linuxpath': str(test_file),
            'reread_on_query': 'false',
            'host': 'localhost',
            'port': '8888',
            'max_connections': '100',
            'max_payload_size': '1024',
            'connection_timeout': '5',
            'tcp_nodelay': 'true',
            'socket_buffer_size': '262144',
            'ssl_enabled': 'false',
            'ssl_cert_file': 'certs/server.crt',
            'ssl_key_file': 'certs/server.key',
            'min_tls_version': 'TLSv1.3',
            'verify_client_cert': 'false',
            'ca_file': 'certs/ca.crt',
            'log_level': 'DEBUG',
            'log_file': 'logs/server.log',
            'thread_pool_size': '50'
        }
        defaults.update(overrides)
        
        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {defaults['linuxpath']}
reread_on_query = {defaults['reread_on_query']}

[server]
host = {defaults['host']}
port = {defaults['port']}
max_connections = {defaults['max_connections']}
max_payload_size = {defaults['max_payload_size']}
connection_timeout = {defaults['connection_timeout']}
tcp_nodelay = {defaults['tcp_nodelay']}
socket_buffer_size = {defaults['socket_buffer_size']}

[ssl]
ssl_enabled = {defaults['ssl_enabled']}
ssl_cert_file = {defaults['ssl_cert_file']}
ssl_key_file = {defaults['ssl_key_file']}
min_tls_version = {defaults['min_tls_version']}
verify_client_cert = {defaults['verify_client_cert']}
ca_file = {defaults['ca_file']}

[logging]
log_level = {defaults['log_level']}
log_file = {defaults['log_file']}
thread_pool_size = {defaults['thread_pool_size']}
"""
        config_file.write_text(config_content)
        return config_file

    def test_load_config_with_valid_file(self, temp_dir: Path):
        """Test load_config with a valid configuration file."""
        config_file = self.create_valid_config(temp_dir)
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify return types
        assert isinstance(default_config, dict)
        assert isinstance(server_config, dict)
        assert isinstance(ssl_config, dict)
        assert isinstance(logging_config, dict)
        
        # Verify default config values and types
        assert isinstance(default_config["linuxpath"], str)
        assert isinstance(default_config["reread_on_query"], bool)
        assert default_config["reread_on_query"] is False
        
        # Verify server config values and types
        assert server_config["host"] == "localhost"
        assert server_config["port"] == 8888
        assert isinstance(server_config["port"], int)
        assert server_config["max_connections"] == 100
        assert isinstance(server_config["max_connections"], int)
        assert server_config["max_payload_size"] == 1024
        assert isinstance(server_config["max_payload_size"], int)
        assert server_config["connection_timeout"] == 5
        assert isinstance(server_config["connection_timeout"], int)
        assert server_config["tcp_nodelay"] is True
        assert isinstance(server_config["tcp_nodelay"], bool)
        assert server_config["socket_buffer_size"] == 262144
        assert isinstance(server_config["socket_buffer_size"], int)
        
        # Verify SSL config values and types
        assert ssl_config["ssl_enabled"] is False
        assert isinstance(ssl_config["ssl_enabled"], bool)
        # SSL cert file should be resolved to absolute path
        assert "certs/server.crt" in ssl_config["ssl_cert_file"]
        assert isinstance(ssl_config["ssl_cert_file"], str)
        assert "certs/server.key" in ssl_config["ssl_key_file"]
        assert isinstance(ssl_config["ssl_key_file"], str)
        assert ssl_config["min_tls_version"] == "TLSv1.3"
        assert isinstance(ssl_config["min_tls_version"], str)
        assert ssl_config["verify_client_cert"] is False
        assert isinstance(ssl_config["verify_client_cert"], bool)
        assert "certs/ca.crt" in ssl_config["ca_file"]
        assert isinstance(ssl_config["ca_file"], str)
        
        # Verify logging config values and types
        assert logging_config["log_level"] == "DEBUG"
        assert isinstance(logging_config["log_level"], str)
        assert logging_config["log_file"] == "logs/server.log"
        assert isinstance(logging_config["log_file"], str)
        assert logging_config["thread_pool_size"] == 50
        assert isinstance(logging_config["thread_pool_size"], int)

    def test_load_config_with_ssl_enabled(self, temp_dir: Path):
        """Test load_config with SSL enabled."""
        config_file = self.create_valid_config(temp_dir, ssl_enabled='true', verify_client_cert='true')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert ssl_config["ssl_enabled"] is True
        assert ssl_config["verify_client_cert"] is True

    def test_load_config_with_reread_enabled(self, temp_dir: Path):
        """Test load_config with reread_on_query enabled."""
        config_file = self.create_valid_config(temp_dir, reread_on_query='true')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert default_config["reread_on_query"] is True

    def test_load_config_with_different_port(self, temp_dir: Path):
        """Test load_config with different port numbers."""
        # Test port 0 (auto-assign)
        config_file = self.create_valid_config(temp_dir, port='0')
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        assert server_config["port"] == 0
        
        # Test high port number
        config_file = self.create_valid_config(temp_dir, port='65535')
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        assert server_config["port"] == 65535

    def test_load_config_with_different_log_levels(self, temp_dir: Path):
        """Test load_config with different log levels."""
        log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        
        for level in log_levels:
            config_file = self.create_valid_config(temp_dir, log_level=level)
            default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
            assert logging_config["log_level"] == level

    def test_load_config_with_different_tls_versions(self, temp_dir: Path):
        """Test load_config with different TLS versions."""
        tls_versions = ['TLSv1.2', 'TLSv1.3']
        
        for version in tls_versions:
            config_file = self.create_valid_config(temp_dir, min_tls_version=version)
            default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
            assert ssl_config["min_tls_version"] == version

    def test_load_config_with_tcp_nodelay_disabled(self, temp_dir: Path):
        """Test load_config with TCP_NODELAY disabled."""
        config_file = self.create_valid_config(temp_dir, tcp_nodelay='false')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert server_config["tcp_nodelay"] is False

    def test_load_config_with_large_buffer_sizes(self, temp_dir: Path):
        """Test load_config with large buffer sizes."""
        config_file = self.create_valid_config(temp_dir, 
                                               socket_buffer_size='1048576',
                                               max_payload_size='4096')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert server_config["socket_buffer_size"] == 1048576
        assert server_config["max_payload_size"] == 4096

    def test_load_config_with_high_connection_limits(self, temp_dir: Path):
        """Test load_config with high connection limits."""
        config_file = self.create_valid_config(temp_dir, 
                                               max_connections='1000',
                                               thread_pool_size='200')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert server_config["max_connections"] == 1000
        assert logging_config["thread_pool_size"] == 200

    def test_load_config_with_long_timeout(self, temp_dir: Path):
        """Test load_config with long connection timeout."""
        config_file = self.create_valid_config(temp_dir, connection_timeout='30')
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        assert server_config["connection_timeout"] == 30

    def test_load_config_without_file_parameter(self):
        """Test load_config without providing config file parameter."""
        # Should use default config.ini
        try:
            default_config, server_config, ssl_config, logging_config = load_config()
            # If this succeeds, verify the types
            assert isinstance(default_config, dict)
            assert isinstance(server_config, dict)
            assert isinstance(ssl_config, dict)
            assert isinstance(logging_config, dict)
        except ConfigurationError:
            # This is expected if config.ini doesn't exist
            pass

    def test_load_config_with_none_parameter(self):
        """Test load_config with None as config file parameter."""
        try:
            default_config, server_config, ssl_config, logging_config = load_config(None)
            # If this succeeds, verify the types
            assert isinstance(default_config, dict)
            assert isinstance(server_config, dict)
            assert isinstance(ssl_config, dict)
            assert isinstance(logging_config, dict)
        except ConfigurationError:
            # This is expected if config.ini doesn't exist
            pass

    def test_load_config_with_empty_string_parameter(self):
        """Test load_config with empty string as config file parameter."""
        try:
            default_config, server_config, ssl_config, logging_config = load_config("")
            # If this succeeds, verify the types
            assert isinstance(default_config, dict)
            assert isinstance(server_config, dict)
            assert isinstance(ssl_config, dict)
            assert isinstance(logging_config, dict)
        except ConfigurationError:
            # This is expected if config.ini doesn't exist
            pass


class TestConfigurationErrorScenarios:
    """Test various configuration error scenarios."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_missing_config_file_error(self):
        """Test ConfigurationError when config file doesn't exist."""
        with pytest.raises(ConfigurationError, match="Configuration file not found"):
            load_config("/nonexistent/path/config.ini")

    def test_unreadable_config_file_error(self, temp_dir: Path):
        """Test ConfigurationError when config file is not readable."""
        config_file = temp_dir / "unreadable.ini"
        config_file.write_text("[DEFAULT]\nlinuxpath = /test/file.txt\n")
        config_file.chmod(0o000)  # Remove all permissions
        
        try:
            with pytest.raises(ConfigurationError):
                load_config(str(config_file))
        finally:
            config_file.chmod(0o644)  # Restore permissions for cleanup

    def test_invalid_ini_syntax_error(self, temp_dir: Path):
        """Test ConfigurationError with invalid INI syntax."""
        config_file = temp_dir / "invalid.ini"
        config_file.write_text("""[DEFAULT
linuxpath = /test/file.txt
missing closing bracket
""")

        with pytest.raises(ConfigurationError):
            load_config(str(config_file))

    def test_missing_default_section_error(self, temp_dir: Path):
        """Test ConfigurationError when DEFAULT section is missing."""
        config_file = temp_dir / "no_default.ini"
        config_file.write_text("""[server]
host = localhost
port = 8888
""")

        with pytest.raises(ConfigurationError, match="Missing required parameter"):
            load_config(str(config_file))

    def test_missing_server_section_error(self, temp_dir: Path):
        """Test ConfigurationError when server section is missing."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "no_server.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false
""")

        with pytest.raises(ConfigurationError, match="Missing required parameter"):
            load_config(str(config_file))

    def test_missing_ssl_section_error(self, temp_dir: Path):
        """Test that missing SSL section uses defaults."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "no_ssl.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = 8888
""")

        # Should not raise an error, should use defaults
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))

        # Verify SSL defaults
        assert ssl_config["ssl_enabled"] is False
        assert "server.crt" in ssl_config["ssl_cert_file"]
        assert "server.key" in ssl_config["ssl_key_file"]

    def test_missing_logging_section_error(self, temp_dir: Path):
        """Test that missing logging section uses defaults."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "no_logging.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = 8888

[ssl]
ssl_enabled = false
""")

        # Should not raise an error, should use defaults
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))

        # Verify logging defaults
        assert logging_config["log_level"] == "DEBUG"
        assert logging_config["log_file"] == "logs/server.log"
        assert logging_config["thread_pool_size"] == 50

    def test_missing_linuxpath_parameter_error(self, temp_dir: Path):
        """Test ConfigurationError when linuxpath parameter is missing."""
        config_file = temp_dir / "no_linuxpath.ini"
        config_file.write_text("""[DEFAULT]
reread_on_query = false

[server]
host = localhost
port = 8888

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

        with pytest.raises(ConfigurationError, match="Missing required parameter.*linuxpath"):
            load_config(str(config_file))

    def test_missing_host_parameter_error(self, temp_dir: Path):
        """Test ConfigurationError when host parameter is missing."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "no_host.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
port = 8888

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

        with pytest.raises(ConfigurationError, match="Missing required parameter.*host"):
            load_config(str(config_file))

    def test_missing_port_parameter_error(self, temp_dir: Path):
        """Test ConfigurationError when port parameter is missing."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "no_port.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

        with pytest.raises(ConfigurationError, match="Missing required parameter.*port"):
            load_config(str(config_file))

    def test_invalid_boolean_values_error(self, temp_dir: Path):
        """Test ConfigurationError with various invalid boolean values."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        # Valid booleans that should work (including case variations)
        valid_booleans = ['yes', 'no', '1', '0', 'true', 'false', 'True', 'False']
        for valid_bool in valid_booleans:
            config_file = temp_dir / f"valid_bool_{valid_bool}.ini"
            config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {valid_bool}

[server]
host = localhost
port = 8888

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")
            # Should not raise an error
            load_config(str(config_file))

        # Invalid booleans that should fail
        invalid_booleans = ['invalid', 'maybe', 'on', 'off', '2', '-1']
        for invalid_bool in invalid_booleans:
            config_file = temp_dir / f"invalid_bool_{invalid_bool}.ini"
            config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {invalid_bool}

[server]
host = localhost
port = 8888

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

            with pytest.raises(ConfigurationError, match="Invalid boolean value"):
                load_config(str(config_file))

    def test_invalid_integer_values_error(self, temp_dir: Path):
        """Test ConfigurationError with various invalid integer values."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        invalid_integers = ['abc', '12.5', 'port', '8888.0', '1e3', 'infinity']

        for invalid_int in invalid_integers:
            config_file = temp_dir / f"invalid_int_{invalid_int.replace('.', '_')}.ini"
            config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = {invalid_int}

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

            with pytest.raises(ConfigurationError, match="Invalid integer value"):
                load_config(str(config_file))

    def test_negative_integer_values_error(self, temp_dir: Path):
        """Test ConfigurationError with negative integer values where not allowed."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        # Test negative port
        config_file = temp_dir / "negative_port.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = -1

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

        with pytest.raises(ConfigurationError, match="Port must be between 0 and 65535"):
            load_config(str(config_file))

    def test_port_out_of_range_error(self, temp_dir: Path):
        """Test ConfigurationError with port numbers out of valid range."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        invalid_ports = [65536, 70000, 100000]

        for invalid_port in invalid_ports:
            config_file = temp_dir / f"invalid_port_{invalid_port}.ini"
            config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = {invalid_port}

[ssl]
ssl_enabled = false

[logging]
log_level = DEBUG
""")

            with pytest.raises(ConfigurationError, match="Port must be between 0 and 65535"):
                load_config(str(config_file))

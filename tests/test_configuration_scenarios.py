#!/usr/bin/env python3
"""
Comprehensive test script for all configuration scenarios.

This script tests all 4 configuration combinations:
1. SSL=true, REREAD_ON_QUERY=true
2. SSL=true, REREAD_ON_QUERY=false  
3. SSL=false, REREAD_ON_QUERY=true
4. SSL=false, REREAD_ON_QUERY=false
"""

import os
import sys
import tempfile
import time
import threading
import pytest
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

from config_loader import load_config, ConfigurationError
from server import FileSearchEngine, StringSearchServer, create_ssl_context
from client import SearchClient


def create_test_config(temp_dir: Path, test_file: Path, ssl_enabled: bool, reread_on_query: bool) -> Path:
    """Create a test configuration file."""
    config_file = temp_dir / "config.ini"
    
    config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {str(reread_on_query).lower()}

[server]
host = localhost
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = {str(ssl_enabled).lower()}
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
    config_file.write_text(config_content)
    return config_file


def test_configuration_scenarios() -> None:
    """Test all configuration scenarios."""
    scenarios = [
        ("SSL=true, REREAD=true", True, True),
        ("SSL=true, REREAD=false", True, False),
        ("SSL=false, REREAD=true", False, True),
        ("SSL=false, REREAD=false", False, False),
    ]

    for scenario_name, ssl_enabled, reread_on_query in scenarios:
        print(f"\n🧪 Testing {scenario_name}")
        print(f"   SSL: {ssl_enabled}, REREAD_ON_QUERY: {reread_on_query}")

        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                # Create test file
                test_file = temp_path / "test.txt"
                test_file.write_text("test line 1\ntest line 2\ntest line 3\n")

                # Create configuration
                config_file = create_test_config(temp_path, test_file, ssl_enabled, reread_on_query)

                # Test configuration loading
                default_config, server_config, ssl_config, logging_config = load_config(str(config_file))

                # Verify configuration values
                assert default_config["reread_on_query"] == reread_on_query
                assert ssl_config["ssl_enabled"] == ssl_enabled
                print(f"   ✅ Configuration loaded correctly")

                # Test search engine
                search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])

                # Test search performance
                start_time = time.perf_counter()
                result = search_engine.search("test line 1")
                end_time = time.perf_counter()

                execution_time_ms = (end_time - start_time) * 1000
                max_time_ms = 40.0 if reread_on_query else 0.5

                assert result is True
                print(f"   ✅ Search works: {execution_time_ms:.2f}ms (limit: {max_time_ms}ms)")

                # Test SSL context creation
                try:
                    ssl_context = create_ssl_context(ssl_config)
                    if ssl_enabled:
                        # SSL context creation might fail due to missing certificates, which is expected
                        print(f"   ✅ SSL context creation attempted (enabled)")
                    else:
                        assert ssl_context is None
                        print(f"   ✅ SSL context correctly disabled")
                except Exception as e:
                    if ssl_enabled:
                        # Expected to fail due to missing certificates
                        print(f"   ✅ SSL context creation failed as expected: {e}")
                    else:
                        raise

                # Test server initialization (without starting)
                server = StringSearchServer(
                    ("localhost", 0),
                    default_config,
                    server_config,
                    ssl_config,
                    logging_config,
                    search_engine
                )

                assert server is not None
                print(f"   ✅ Server initialization successful")

                # Clean up
                server.server_close()

        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            pytest.fail(f"Configuration scenario {scenario_name} failed: {e}")


def main():
    """Run comprehensive configuration scenario tests."""
    print("🎯 COMPREHENSIVE CONFIGURATION SCENARIO TESTING")
    print("=" * 60)
    
    scenarios = [
        ("SSL=true, REREAD=true", True, True),
        ("SSL=true, REREAD=false", True, False),
        ("SSL=false, REREAD=true", False, True),
        ("SSL=false, REREAD=false", False, False),
    ]
    
    results = []
    
    for scenario_name, ssl_enabled, reread_on_query in scenarios:
        success = test_configuration_scenario(scenario_name, ssl_enabled, reread_on_query)
        results.append((scenario_name, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for scenario_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{scenario_name:25} | {status}")
        if success:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} scenarios passed")
    
    if passed == len(results):
        print("🎉 ALL CONFIGURATION SCENARIOS PASSED!")
        return True
    else:
        print("❌ Some configuration scenarios failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

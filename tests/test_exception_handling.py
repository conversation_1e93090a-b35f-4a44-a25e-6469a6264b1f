#!/usr/bin/env python3
"""
Comprehensive Exception Handling Test Suite

This module tests all exception scenarios across the entire codebase,
ensuring robust error handling and proper exception propagation.

Test Coverage:
- FileSearchError exceptions in all contexts
- ConfigurationError exceptions
- Network and SSL exceptions
- File I/O exceptions
- Unicode and encoding exceptions
- Timeout and connection exceptions
- Search algorithm exceptions
- Server startup and shutdown exceptions

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import threading
import time
import socket
import ssl
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from config_loader import (
    load_config, ConfigurationError, DefaultConfig, 
    ServerConfig, SSLConfig, LoggingConfig
)
from server import (
    FileSearchEngine, StringSearchServer, StringSearchHandler,
    create_ssl_context, FileSearchError, setup_logging
)
from client import SearchClient
from search_algorithms_new import (
    HashSet<PERSON>earch, <PERSON>ar<PERSON><PERSON><PERSON>, <PERSON>ary<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>p<PERSON><PERSON><PERSON>, MMapSearch
)


class TestFileSearchEngineExceptions:
    """Test exception handling in FileSearchEngine."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_file_not_found_exception(self):
        """Test FileSearchError when file doesn't exist."""
        with pytest.raises(FileSearchError, match="File not found"):
            FileSearchEngine("/nonexistent/file.txt", reread_on_query=False)

    def test_file_not_readable_exception(self, temp_dir: Path):
        """Test FileSearchError when file is not readable."""
        test_file = temp_dir / "unreadable.txt"
        test_file.write_text("test content")
        test_file.chmod(0o000)  # Remove all permissions
        
        try:
            with pytest.raises(FileSearchError, match="File not readable"):
                FileSearchEngine(str(test_file), reread_on_query=False)
        finally:
            test_file.chmod(0o644)  # Restore permissions for cleanup

    def test_search_operation_exception(self, temp_dir: Path):
        """Test FileSearchError during search operation."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\n")
        
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        # Mock the searcher to raise an exception
        with patch.object(engine.searcher, 'search', side_effect=Exception("Search failed")):
            with pytest.raises(FileSearchError, match="Search failed"):
                engine.search("test query")

    def test_file_deletion_during_reread_mode(self, temp_dir: Path):
        """Test exception when file is deleted during reread mode operation."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\n")
        
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        # Delete the file
        test_file.unlink()
        
        # Should raise FileSearchError when trying to search
        with pytest.raises(FileSearchError):
            engine.search("test query")

    def test_permission_denied_during_search(self, temp_dir: Path):
        """Test exception when file permissions change during operation."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\n")
        
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        # Remove read permissions
        test_file.chmod(0o000)
        
        try:
            with pytest.raises(FileSearchError):
                engine.search("test query")
        finally:
            test_file.chmod(0o644)  # Restore permissions

    def test_corrupted_file_during_search(self, temp_dir: Path):
        """Test exception handling with corrupted file content."""
        test_file = temp_dir / "test.txt"
        
        # Create file with invalid UTF-8 sequences
        with open(test_file, 'wb') as f:
            f.write(b"valid line 1\n")
            f.write(b"\xff\xfe\x00\x00invalid utf-8\n")  # Invalid UTF-8
            f.write(b"valid line 2\n")
        
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        # Should handle encoding errors gracefully
        result = engine.search("valid line 1")
        assert result is True  # Should still find valid lines


class TestConfigurationExceptions:
    """Test exception handling in configuration loading."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_missing_config_file_exception(self):
        """Test ConfigurationError when config file doesn't exist."""
        with pytest.raises(ConfigurationError, match="Configuration file not found"):
            load_config("/nonexistent/config.ini")

    def test_invalid_config_syntax_exception(self, temp_dir: Path):
        """Test ConfigurationError with invalid INI syntax."""
        config_file = temp_dir / "invalid.ini"
        config_file.write_text("""[DEFAULT
linuxpath = /test/file.txt
invalid syntax here
""")
        
        with pytest.raises(ConfigurationError):
            load_config(str(config_file))

    def test_missing_required_parameter_exception(self, temp_dir: Path):
        """Test ConfigurationError when required parameters are missing."""
        config_file = temp_dir / "incomplete.ini"
        config_file.write_text("""[DEFAULT]
# Missing linuxpath
reread_on_query = false

[server]
host = localhost
port = 8888
""")
        
        with pytest.raises(ConfigurationError, match="Missing required parameter"):
            load_config(str(config_file))

    def test_invalid_boolean_value_exception(self, temp_dir: Path):
        """Test ConfigurationError with invalid boolean values."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        config_file = temp_dir / "invalid_bool.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = invalid_boolean

[server]
host = localhost
port = 8888
""")
        
        with pytest.raises(ConfigurationError, match="Invalid boolean value"):
            load_config(str(config_file))

    def test_invalid_integer_value_exception(self, temp_dir: Path):
        """Test ConfigurationError with invalid integer values."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        config_file = temp_dir / "invalid_int.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = invalid_port
""")
        
        with pytest.raises(ConfigurationError, match="Invalid integer value"):
            load_config(str(config_file))

    def test_negative_port_exception(self, temp_dir: Path):
        """Test ConfigurationError with negative port number."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        config_file = temp_dir / "negative_port.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = -1
""")
        
        with pytest.raises(ConfigurationError, match="Port must be between 0 and 65535"):
            load_config(str(config_file))

    def test_port_out_of_range_exception(self, temp_dir: Path):
        """Test ConfigurationError with port number out of range."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        config_file = temp_dir / "invalid_port_range.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = 70000
""")
        
        with pytest.raises(ConfigurationError, match="Port must be between 0 and 65535"):
            load_config(str(config_file))


class TestSSLExceptions:
    """Test SSL-related exception handling."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_ssl_missing_cert_file_exception(self):
        """Test FileSearchError when SSL cert file is missing."""
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": "/nonexistent/server.crt",
            "ssl_key_file": "/nonexistent/server.key",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "/nonexistent/ca.crt"
        }
        
        with pytest.raises(FileSearchError, match="SSL certificate file not found"):
            create_ssl_context(ssl_config)

    def test_ssl_missing_key_file_exception(self, temp_dir: Path):
        """Test FileSearchError when SSL key file is missing."""
        # Create cert file but not key file
        cert_file = temp_dir / "server.crt"
        cert_file.write_text("dummy cert content")
        
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": str(cert_file),
            "ssl_key_file": "/nonexistent/server.key",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "/nonexistent/ca.crt"
        }
        
        with pytest.raises(FileSearchError, match="SSL key file not found"):
            create_ssl_context(ssl_config)

    def test_ssl_empty_cert_path_exception(self):
        """Test FileSearchError when SSL cert path is empty."""
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": "",
            "ssl_key_file": "server.key",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "ca.crt"
        }
        
        with pytest.raises(FileSearchError, match="SSL certificate file path not configured"):
            create_ssl_context(ssl_config)

    def test_ssl_empty_key_path_exception(self):
        """Test FileSearchError when SSL key path is empty."""
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": "server.crt",
            "ssl_key_file": "",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "ca.crt"
        }
        
        with pytest.raises(FileSearchError, match="SSL key file path not configured"):
            create_ssl_context(ssl_config)

    def test_ssl_empty_tls_version_exception(self):
        """Test FileSearchError when TLS version is empty."""
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": "server.crt",
            "ssl_key_file": "server.key",
            "min_tls_version": "",
            "verify_client_cert": False,
            "ca_file": "ca.crt"
        }
        
        with pytest.raises(FileSearchError, match="Minimum TLS version not configured"):
            create_ssl_context(ssl_config)


class TestNetworkExceptions:
    """Test network-related exception handling."""

    def test_client_connection_timeout_exception(self):
        """Test timeout exception in client connection."""
        client = SearchClient("*********", 12345, timeout=0.1)  # Use TEST-NET-1 IP

        with pytest.raises(TimeoutError, match="Connection to *********:12345 timed out"):
            client.send_query("test query")

    def test_client_connection_refused_exception(self):
        """Test connection refused exception."""
        client = SearchClient("localhost", 65432, timeout=1.0)  # Unlikely to be in use

        with pytest.raises(ConnectionError, match="Failed to connect to localhost:65432"):
            client.send_query("test query")

    def test_client_invalid_host_exception(self):
        """Test connection error with invalid hostname."""
        client = SearchClient("invalid.nonexistent.hostname", 8888, timeout=1.0)

        with pytest.raises(ConnectionError):
            client.send_query("test query")

    @patch('socket.socket')
    def test_client_query_too_long_exception(self, mock_socket_class):
        """Test ValueError when query exceeds maximum length."""
        client = SearchClient("localhost", 8888)

        # Create a query longer than 1024 bytes
        long_query = "x" * 1025

        with pytest.raises(ValueError, match="Query too long"):
            client.send_query(long_query)

    def test_client_persistent_connection_failure(self):
        """Test persistent connection failure handling."""
        client = SearchClient("*********", 12345, timeout=0.1)

        with pytest.raises(TimeoutError):
            client.connect()

    def test_client_ssl_connection_failure(self):
        """Test SSL connection failure."""
        client = SearchClient("localhost", 8888, use_ssl=True, timeout=1.0)

        with pytest.raises(ConnectionError):
            client.send_query("test query")


class TestSearchAlgorithmExceptions:
    """Test exception handling in search algorithms."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_hashset_search_no_file_loaded_exception(self):
        """Test ValueError when searching without loading file."""
        searcher = HashSetSearch()

        with pytest.raises(ValueError, match="File not loaded"):
            searcher.search("test query")

    def test_linear_search_no_file_loaded_exception(self):
        """Test ValueError when searching without loading file."""
        searcher = LinearSearch()

        with pytest.raises(ValueError, match="File not loaded"):
            searcher.search("test query")

    def test_binary_search_no_file_loaded_exception(self):
        """Test ValueError when searching without loading file."""
        searcher = BinarySearch()

        with pytest.raises(ValueError, match="File not loaded"):
            searcher.search("test query")

    def test_mmap_search_no_file_loaded_exception(self):
        """Test ValueError when searching without loading file."""
        searcher = MMapSearch()

        with pytest.raises(ValueError, match="File not loaded"):
            searcher.search("test query")

    def test_grep_search_no_file_loaded_exception(self):
        """Test ValueError when searching without loading file."""
        searcher = GrepSearch()

        with pytest.raises(ValueError, match="File not loaded"):
            searcher.search("test query")

    def test_hashset_search_file_not_found_exception(self):
        """Test FileNotFoundError when loading non-existent file."""
        searcher = HashSetSearch()

        with pytest.raises(FileNotFoundError):
            searcher.load("/nonexistent/file.txt")

    def test_linear_search_file_not_found_exception(self):
        """Test FileNotFoundError when loading non-existent file."""
        searcher = LinearSearch()

        with pytest.raises(FileNotFoundError):
            searcher.load("/nonexistent/file.txt")

    def test_binary_search_file_not_found_exception(self):
        """Test FileNotFoundError when loading non-existent file."""
        searcher = BinarySearch()

        with pytest.raises(FileNotFoundError):
            searcher.load("/nonexistent/file.txt")

    def test_mmap_search_file_not_found_exception(self):
        """Test FileNotFoundError when loading non-existent file."""
        searcher = MMapSearch()

        with pytest.raises(FileNotFoundError):
            searcher.load("/nonexistent/file.txt")

    def test_grep_search_file_not_found_exception(self):
        """Test FileNotFoundError when loading non-existent file."""
        searcher = GrepSearch()

        with pytest.raises(FileNotFoundError):
            searcher.load("/nonexistent/file.txt")

    def test_hashset_search_permission_denied_exception(self, temp_dir: Path):
        """Test PermissionError when loading unreadable file."""
        test_file = temp_dir / "unreadable.txt"
        test_file.write_text("test content")
        test_file.chmod(0o000)

        searcher = HashSetSearch()

        try:
            with pytest.raises(PermissionError):
                searcher.load(str(test_file))
        finally:
            test_file.chmod(0o644)

    def test_mmap_search_permission_denied_exception(self, temp_dir: Path):
        """Test PermissionError when memory mapping unreadable file."""
        test_file = temp_dir / "unreadable.txt"
        test_file.write_text("test content")
        test_file.chmod(0o000)

        searcher = MMapSearch()

        try:
            with pytest.raises(PermissionError):
                searcher.load(str(test_file))
        finally:
            test_file.chmod(0o644)

    def test_grep_command_not_found_exception(self, temp_dir: Path):
        """Test FileNotFoundError when grep command is not available."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        searcher = GrepSearch()
        searcher.load(str(test_file))

        # Mock subprocess.run to simulate grep not found
        with patch('subprocess.run', side_effect=FileNotFoundError("grep command not found")):
            with pytest.raises(FileNotFoundError):
                searcher.search("test")


class TestServerExceptions:
    """Test server-related exception handling."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_server_bind_address_in_use_exception(self, temp_dir: Path):
        """Test exception when server address is already in use."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        # Create first server
        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = localhost
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = false
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)

        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])

        server1 = StringSearchServer(
            ("localhost", 0),
            default_config, server_config, ssl_config, logging_config, search_engine
        )

        actual_port = server1.server_address[1]

        try:
            # Try to create second server on same port
            with pytest.raises(OSError):  # Address already in use
                server2 = StringSearchServer(
                    ("localhost", actual_port),
                    default_config, server_config, ssl_config, logging_config, search_engine
                )
        finally:
            server1.server_close()

    def test_server_invalid_host_exception(self, temp_dir: Path):
        """Test exception with invalid host address."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false

[server]
host = invalid.nonexistent.hostname
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = false
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)

        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])

        with pytest.raises(OSError):  # Name resolution failure
            server = StringSearchServer(
                ("invalid.nonexistent.hostname", 0),
                default_config, server_config, ssl_config, logging_config, search_engine
            )

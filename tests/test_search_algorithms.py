#!/usr/bin/env python3
"""
Comprehensive tests for search algorithms.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, Mock
from search_algorithms_new import (
    HashSetSearch, LinearSearch, BinarySearch, MMapSearch,
    GrepSearch, SearchAlgorithm
)


class TestHashSetSearch:
    """Test cases for HashSetSearch algorithm."""

    def test_initialization(self):
        """Test HashSetSearch initialization."""
        search = HashSetSearch()
        assert search.name() == "Hash Set (FrozenSet)"
        assert search.lines is None

    def test_load_file(self):
        """Test loading a file into HashSetSearch."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = HashSetSearch()
            search.load(file_path)
            assert search.lines is not None
            assert len(search.lines) == 3
            assert "apple" in search.lines
            assert "banana" in search.lines
            assert "cherry" in search.lines
        finally:
            os.unlink(file_path)

    def test_search_existing_string(self):
        """Test searching for existing strings."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = HashSetSearch()
            search.load(file_path)
            assert search.search("apple") is True
            assert search.search("banana") is True
            assert search.search("cherry") is True
        finally:
            os.unlink(file_path)

    def test_search_non_existing_string(self):
        """Test searching for non-existing strings."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = HashSetSearch()
            search.load(file_path)
            assert search.search("orange") is False
            assert search.search("grape") is False
        finally:
            os.unlink(file_path)

    def test_search_without_load(self):
        """Test searching without loading a file first."""
        search = HashSetSearch()
        with pytest.raises(ValueError, match="File not loaded"):
            search.search("test")


class TestLinearSearch:
    """Test cases for LinearSearch algorithm."""

    def test_initialization(self):
        """Test LinearSearch initialization."""
        search = LinearSearch()
        assert search.name() == "Linear Search (Optimized)"
        assert search.file_path is None

    def test_load_and_search(self):
        """Test loading and searching with LinearSearch."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = LinearSearch()
            search.load(file_path)
            assert search.search("banana") is True
            assert search.search("orange") is False
        finally:
            os.unlink(file_path)


class TestBinarySearch:
    """Test cases for BinarySearch algorithm."""

    def test_initialization(self):
        """Test BinarySearch initialization."""
        search = BinarySearch()
        assert search.name() == "Binary Search (Deduplicated)"
        assert search.lines is None

    def test_load_and_search(self):
        """Test loading and searching with BinarySearch."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\nbanana\n")  # Include duplicate
            file_path = f.name

        try:
            search = BinarySearch()
            search.load(file_path)
            assert search.search("banana") is True
            assert search.search("orange") is False
        finally:
            os.unlink(file_path)


class TestMMapSearch:
    """Test cases for MMapSearch algorithm."""

    def test_initialization(self):
        """Test MMapSearch initialization."""
        search = MMapSearch()
        assert search.name() == "Memory-Mapped"
        assert search.mm is None

    def test_load_and_search(self):
        """Test loading and searching with MMapSearch."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = MMapSearch()
            search.load(file_path)
            assert search.search("banana") is True
            assert search.search("orange") is False
        finally:
            os.unlink(file_path)

    def test_search_without_load(self):
        """Test searching without loading a file first."""
        search = MMapSearch()
        with pytest.raises(ValueError, match="File not loaded"):
            search.search("test")


class TestGrepSearch:
    """Test cases for GrepSearch algorithm."""

    def test_initialization(self):
        """Test GrepSearch initialization."""
        search = GrepSearch()
        assert search.name() == "Native Grep"

    @patch('subprocess.run')
    def test_search_found(self, mock_run):
        """Test GrepSearch when string is found."""
        mock_run.return_value.returncode = 0

        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("test_query\nother_line\n")
            temp_file = f.name

        try:
            search = GrepSearch()
            search.load(temp_file)
            result = search.search("test_query")

            assert result is True
            mock_run.assert_called_once()
        finally:
            Path(temp_file).unlink(missing_ok=True)

    @patch('subprocess.run')
    def test_search_not_found(self, mock_run):
        """Test GrepSearch when string is not found."""
        mock_run.return_value.returncode = 1

        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("other_content\nanother_line\n")
            temp_file = f.name

        try:
            search = GrepSearch()
            search.load(temp_file)
            result = search.search("test_query")

            assert result is False
        finally:
            Path(temp_file).unlink(missing_ok=True)

    @patch('subprocess.run')
    def test_search_error(self, mock_run):
        """Test GrepSearch when grep command fails."""
        mock_run.return_value.returncode = 2

        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("test_content\nanother_line\n")
            temp_file = f.name

        try:
            search = GrepSearch()
            search.load(temp_file)
            result = search.search("test_query")

            assert result is False
        finally:
            Path(temp_file).unlink(missing_ok=True)

    def test_search_without_load(self):
        """Test searching without loading a file first."""
        search = GrepSearch()
        with pytest.raises(ValueError, match="File not loaded"):
            search.search("test")


class TestHashSetSearchReread:
    """Test cases for HashSetSearch with reread mode."""

    def test_initialization(self):
        """Test HashSetSearch with reread mode initialization."""
        search = HashSetSearch(reread_on_query=True)
        assert search.name() == "Hash Set (FrozenSet) (Reread)"
        assert search.file_path is None

    def test_load_and_search(self):
        """Test loading and searching with HashSetSearch in reread mode."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("apple\nbanana\ncherry\n")
            file_path = f.name

        try:
            search = HashSetSearch(reread_on_query=True)
            search.load(file_path)
            assert search.search("banana", file_path) is True
            assert search.search("orange", file_path) is False
        finally:
            os.unlink(file_path)

    def test_search_without_file_path(self):
        """Test searching without providing file_path in reread mode."""
        search = HashSetSearch(reread_on_query=True)
        with pytest.raises(ValueError, match="file_path must be provided"):
            search.search("test")


class TestSearchAlgorithmInterface:
    """Test cases for SearchAlgorithm protocol compliance."""

    def test_all_algorithms_implement_interface(self):
        """Test that all algorithms implement the SearchAlgorithm interface."""
        algorithms = [
            HashSetSearch(),
            LinearSearch(),
            BinarySearch(),
            MMapSearch(),
            GrepSearch(),
            HashSetSearch(reread_on_query=True)
        ]
        
        for algo in algorithms:
            # Test that all required methods exist
            assert hasattr(algo, 'load')
            assert hasattr(algo, 'search')
            assert hasattr(algo, 'name')
            
            # Test that name() returns a string
            assert isinstance(algo.name(), str)
            assert len(algo.name()) > 0


class TestEdgeCases:
    """Test edge cases for search algorithms."""

    def test_empty_file(self):
        """Test algorithms with empty files."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            file_path = f.name  # Empty file

        try:
            algorithms = [HashSetSearch(), LinearSearch(), BinarySearch()]

            for algo in algorithms:
                algo.load(file_path)
                assert algo.search("anything") is False
        finally:
            os.unlink(file_path)

    def test_platform_specific_code_coverage(self):
        """Test platform-specific code paths."""
        import search_algorithms_new
        import sys

        # Test that platform-specific constants are defined
        assert hasattr(search_algorithms_new, 'MAP_POPULATE')
        assert hasattr(search_algorithms_new, 'POSIX_FADV_SEQUENTIAL')
        assert hasattr(search_algorithms_new, 'POSIX_FADV_WILLNEED')

        # Test that constants have appropriate values based on platform
        if sys.platform == 'linux':
            assert search_algorithms_new.MAP_POPULATE == 0x08000
            assert search_algorithms_new.POSIX_FADV_SEQUENTIAL == 2
            assert search_algorithms_new.POSIX_FADV_WILLNEED == 3
        else:
            assert search_algorithms_new.MAP_POPULATE == 0
            assert search_algorithms_new.POSIX_FADV_SEQUENTIAL == 0
            assert search_algorithms_new.POSIX_FADV_WILLNEED == 0

    def test_import_error_handling(self):
        """Test import error handling for fcntl."""
        import search_algorithms_new

        # Test that has_posix_fadvise is defined
        assert hasattr(search_algorithms_new, 'has_posix_fadvise')
        assert isinstance(search_algorithms_new.has_posix_fadvise, bool)

    def test_hashset_search_fast_load_with_reread(self):
        """Test HashSetSearch fast_load method with reread_on_query."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline3\n")
            file_path = f.name

        try:
            search = HashSetSearch(reread_on_query=True)

            # Test successful search
            result = search.fast_load(file_path, "line2")
            assert result is True

            # Test unsuccessful search
            result = search.fast_load(file_path, "nonexistent")
            assert result is False

            # Test with empty query
            result = search.fast_load(file_path, "")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_hashset_search_simple_search_method(self):
        """Test HashSetSearch _simple_search method."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline3\n")
            file_path = f.name

        try:
            search = HashSetSearch()

            # Test successful search
            result = search._simple_search(file_path, b"line2")
            assert result is True

            # Test unsuccessful search
            result = search._simple_search(file_path, b"nonexistent")
            assert result is False

            # Test with empty file path
            result = search._simple_search("", b"line2")
            assert result is False

            # Test with empty query bytes
            result = search._simple_search(file_path, b"")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_hashset_search_exception_handling(self):
        """Test HashSetSearch exception handling in fast_load."""
        search = HashSetSearch(reread_on_query=True)

        # Test with non-existent file
        with pytest.raises(Exception):
            search.fast_load("/nonexistent/file.txt", "query")

    def test_mmap_search_with_index(self):
        """Test MMapSearch with indexing enabled."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline3\n")
            file_path = f.name

        try:
            search = MMapSearch(use_index=True)
            search.load(file_path)

            # Test successful search
            result = search.search("line2")
            assert result is True

            # Test unsuccessful search
            result = search.search("nonexistent")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_mmap_search_without_index(self):
        """Test MMapSearch without indexing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline3\n")
            file_path = f.name

        try:
            search = MMapSearch(use_index=False)
            search.load(file_path)

            # Test successful search
            result = search.search("line2")
            assert result is True

            # Test unsuccessful search
            result = search.search("nonexistent")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_grep_search_with_parallel(self):
        """Test GrepSearch with parallel option."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline3\n")
            file_path = f.name

        try:
            search = GrepSearch(use_parallel=True)
            search.load(file_path)

            # Test name includes parallel
            assert "Parallel" in search.name()

            # Test successful search
            result = search.search("line2")
            assert result is True

            # Test unsuccessful search
            result = search.search("nonexistent")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_binary_search_without_deduplication(self):
        """Test BinarySearch without deduplication."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line1\nline2\nline2\nline3\n")  # Duplicate line2
            file_path = f.name

        try:
            search = BinarySearch(deduplicate=False)
            search.load(file_path)

            # Test name doesn't include deduplicated
            assert "Deduplicated" not in search.name()

            # Test successful search
            result = search.search("line2")
            assert result is True

            # Test unsuccessful search
            result = search.search("nonexistent")
            assert result is False

        finally:
            os.unlink(file_path)

    def test_single_line_file(self):
        """Test algorithms with single line files."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("single_line")
            file_path = f.name

        try:
            algorithms = [HashSetSearch(), LinearSearch(), BinarySearch()]
            
            for algo in algorithms:
                algo.load(file_path)
                assert algo.search("single_line") is True
                assert algo.search("not_found") is False
        finally:
            os.unlink(file_path)

    def test_unicode_content(self):
        """Test algorithms with unicode content."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as f:
            f.write("café\nnaïve\nrésumé\n")
            file_path = f.name

        try:
            algorithms = [HashSetSearch(), LinearSearch(), BinarySearch()]
            
            for algo in algorithms:
                algo.load(file_path)
                assert algo.search("café") is True
                assert algo.search("naïve") is True
                assert algo.search("résumé") is True
                assert algo.search("ascii") is False
        finally:
            os.unlink(file_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

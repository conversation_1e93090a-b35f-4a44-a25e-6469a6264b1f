#!/usr/bin/env python3
"""
TCP String Search Server - Installation Verification Script

This script verifies that all components are properly installed and functional.
It performs comprehensive checks on all requirements and dependencies.

Author: <PERSON>
Date: 2025
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path
from typing import List, Tuple, Dict, Any


def print_header(title: str) -> None:
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_status(message: str, status: bool) -> None:
    """Print a status message with colored indicator."""
    indicator = "✅" if status else "❌"
    print(f"{indicator} {message}")


def check_python_version() -> bool:
    """Check if Python version meets requirements."""
    print_header("Python Version Check")
    
    version = sys.version_info
    required_major, required_minor = 3, 10
    
    print(f"Current Python version: {version.major}.{version.minor}.{version.micro}")
    print(f"Required Python version: {required_major}.{required_minor}+")
    
    is_valid = version.major >= required_major and version.minor >= required_minor
    print_status(f"Python version requirement", is_valid)
    
    return is_valid


def check_core_modules() -> bool:
    """Check if core application modules can be imported."""
    print_header("Core Module Import Check")
    
    modules = [
        "server",
        "client", 
        "search_algorithms_new",
        "generate_performance_report",
        "generate_speed_report",
        "performance_test_client"
    ]
    
    all_good = True
    for module in modules:
        try:
            importlib.import_module(module)
            print_status(f"Import {module}", True)
        except ImportError as e:
            print_status(f"Import {module}: {e}", False)
            all_good = False
    
    return all_good


def check_dependencies() -> bool:
    """Check if all required dependencies are available."""
    print_header("Dependency Check")
    
    # Required dependencies
    required_deps = [
        ("pytest", "Testing framework"),
        ("pytest_cov", "Coverage reporting"),
        ("mypy", "Static type checking"),
        ("psutil", "System monitoring"),
        ("matplotlib", "Data visualization"),
        ("pandas", "Data analysis"),
        ("cryptography", "SSL/TLS support"),
        ("yaml", "Configuration parsing")
    ]
    
    # Optional dependencies
    optional_deps = [
        ("reportlab", "PDF report generation"),
        ("numpy", "Numerical computing")
    ]
    
    all_required = True
    
    print("Required dependencies:")
    for module, description in required_deps:
        try:
            importlib.import_module(module)
            print_status(f"{module:15} - {description}", True)
        except ImportError:
            print_status(f"{module:15} - {description}", False)
            all_required = False
    
    print("\nOptional dependencies:")
    for module, description in optional_deps:
        try:
            importlib.import_module(module)
            print_status(f"{module:15} - {description}", True)
        except ImportError:
            print_status(f"{module:15} - {description} (optional)", True)
    
    return all_required


def check_configuration() -> bool:
    """Check if configuration file is valid."""
    print_header("Configuration Check")
    
    try:
        from server import ServerConfig
        config = ServerConfig()
        
        print_status("Configuration file loads", True)
        print_status(f"File path configured: {config.file_path}", True)
        print_status(f"File exists: {Path(config.file_path).exists()}", Path(config.file_path).exists())
        print_status(f"SSL configuration available", config.getboolean("ssl_enabled", False, "ssl"))
        print_status(f"Server port: {config.getint('port', 8888)}", True)
        
        return True
    except Exception as e:
        print_status(f"Configuration error: {e}", False)
        return False


def check_ssl_certificates() -> bool:
    """Check SSL certificate availability."""
    print_header("SSL Certificate Check")
    
    cert_files = [
        "certs/server.crt",
        "certs/server.key",
        "certs/ca.crt"
    ]
    
    all_present = True
    for cert_file in cert_files:
        exists = Path(cert_file).exists()
        print_status(f"Certificate: {cert_file}", exists)
        if not exists:
            all_present = False
    
    if not all_present:
        print("\n💡 To generate certificates, run: python3 generate_certs.py")
    
    return True  # SSL certificates are optional for basic functionality


def check_test_data() -> bool:
    """Check if test data files exist."""
    print_header("Test Data Check")
    
    test_files = [
        "test_data/200k.txt"
    ]
    
    all_present = True
    for test_file in test_files:
        exists = Path(test_file).exists()
        print_status(f"Test file: {test_file}", exists)
        if not exists:
            all_present = False
    
    return all_present


def check_executable_permissions() -> bool:
    """Check if scripts have proper executable permissions."""
    print_header("Executable Permissions Check")
    
    scripts = [
        "server.py",
        "client.py", 
        "generate_certs.py",
        "install_service.sh",
        "install_user.sh"
    ]
    
    all_executable = True
    for script in scripts:
        path = Path(script)
        if path.exists():
            is_executable = os.access(path, os.X_OK)
            print_status(f"Executable: {script}", is_executable)
            if not is_executable:
                all_executable = False
        else:
            print_status(f"Missing: {script}", False)
            all_executable = False
    
    return all_executable


def run_basic_functionality_test() -> bool:
    """Run a basic functionality test."""
    print_header("Basic Functionality Test")
    
    try:
        # Test server configuration
        from server import ServerConfig, FileSearchEngine
        config = ServerConfig()
        
        # Test search engine
        search_engine = FileSearchEngine(config.file_path, False)
        
        # Test a basic search
        result = search_engine.search("test_string_that_probably_doesnt_exist")
        print_status("Search engine functionality", True)
        
        # Test SSL context creation
        from server import create_ssl_context
        ssl_context = create_ssl_context(config)
        print_status("SSL context creation", ssl_context is not None)
        
        return True
    except Exception as e:
        print_status(f"Functionality test failed: {e}", False)
        return False


def run_type_checking() -> bool:
    """Run mypy type checking."""
    print_header("Type Checking (mypy)")
    
    files_to_check = ["server.py", "client.py"]
    
    all_passed = True
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                result = subprocess.run(
                    ["mypy", "--strict", file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                passed = result.returncode == 0
                print_status(f"Type check: {file_path}", passed)
                if not passed and result.stdout:
                    print(f"  Output: {result.stdout[:200]}...")
                all_passed &= passed
            except (subprocess.TimeoutExpired, FileNotFoundError):
                print_status(f"Type check: {file_path} (mypy not available)", True)
        else:
            print_status(f"Missing file: {file_path}", False)
            all_passed = False
    
    return all_passed


def generate_summary_report(results: Dict[str, bool]) -> None:
    """Generate a summary report of all checks."""
    print_header("Installation Verification Summary")
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"Total checks performed: {total_checks}")
    print(f"Checks passed: {passed_checks}")
    print(f"Checks failed: {total_checks - passed_checks}")
    print(f"Success rate: {(passed_checks/total_checks)*100:.1f}%")
    
    print("\nDetailed results:")
    for check_name, result in results.items():
        print_status(check_name, result)
    
    if all(results.values()):
        print(f"\n🎉 ALL CHECKS PASSED! Installation is complete and functional.")
        print(f"🚀 The TCP String Search Server is ready for deployment.")
    else:
        print(f"\n⚠️  Some checks failed. Please review the issues above.")
        print(f"💡 Most issues can be resolved by running the installation script.")


def main() -> None:
    """Main verification function."""
    print("TCP String Search Server - Installation Verification")
    print("Author: Brian Kimathi")
    print("Date: 2025")
    
    # Run all verification checks
    results = {
        "Python Version": check_python_version(),
        "Core Modules": check_core_modules(),
        "Dependencies": check_dependencies(),
        "Configuration": check_configuration(),
        "SSL Certificates": check_ssl_certificates(),
        "Test Data": check_test_data(),
        "Executable Permissions": check_executable_permissions(),
        "Basic Functionality": run_basic_functionality_test(),
        "Type Checking": run_type_checking()
    }
    
    # Generate summary
    generate_summary_report(results)
    
    # Exit with appropriate code
    if all(results.values()):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
